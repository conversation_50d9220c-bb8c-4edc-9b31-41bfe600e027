````markdown
## Git Subtree Pull Script

To pull updates from a remote repository into a specific directory using `git subtree`, use the following command:

```bash
git subtree pull --prefix=.gitlab/merge_request_templates *******************:0_base/gitlab-template.git main
```
````

### Explanation:

- `--prefix`: Specifies the directory in your repository where the subtree is located.
- `*******************:0_base/gitlab-template.git`: The remote repository URL.
- `main`: The branch of the remote repository to pull from.

Ensure you have the necessary permissions to access the remote repository.

````

```markdown
## Git Subtree Add Script

To add a remote repository as a subtree into a specific directory, use the following command:

```bash
git subtree add --prefix=.gitlab/merge_request_templates *******************:0_base/gitlab-template.git main
````

### Explanation:

- `--prefix`: Specifies the directory in your repository where the subtree will be added.
- `*******************:0_base/gitlab-template.git`: The remote repository URL.
- `main`: The branch of the remote repository to add.

This command initializes the specified directory with the contents of the remote repository.

```

```
