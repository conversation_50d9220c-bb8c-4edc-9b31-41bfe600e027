stages:
  - release_staging
  - rollback_staging

# Define push changelog rules
.push_changelog_rules: &push_changelog_rules
  - if: '$CI_COMMIT_MESSAGE =~ /CHANGELOG\.md/i'
    when: never
  - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH || $CI_COMMIT_BRANCH == "staging"
    when: always
  - when: never
# Define push changelog rules
.push_changelog_rules_manual: &push_changelog_rules_manual
  - if: '$CI_COMMIT_MESSAGE =~ /CHANGELOG\.md/i'
    when: never
  - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH || $CI_COMMIT_BRANCH == "staging"
    when: manual
  - when: never

.release_staging:
  stage: release_staging
  image:
    name: node:18-alpine
  before_script:
    - apk update && apk add openssh-client bash rsync yarn sshpass
    - apk --no-cache --virtual build-dependencies add python3 make g++
    - eval $(ssh-agent -s)
    - bash -c 'ssh-add <(echo "$SSH_PRIVATE_KEY")'
    - mkdir -p ~/.ssh
    - ssh-keyscan -H $SSH_SERVER_IP >> ~/.ssh/known_hosts
    - chmod 644 ~/.ssh/known_hosts
  script:
    #
    - echo "============|| Setup Environment... ||============"
    - DEPLOY_TIME=$(date +"%Y%m%d%H%M%S")
    - echo "=> Deploy time:$DEPLOY_TIME"
    - REMOTE_PROJECT_DIR=$REMOTE_PROJECT_DIR
    - REMOTE_PROJECT_PROD_DIR=$REMOTE_PROJECT_PROD_DIR
    - REMOTE_PROJECT_RELEASE_DIR=$REMOTE_PROJECT_RELEASE_DIR
    - echo "=> Project home dir:$REMOTE_PROJECT_DIR"
    - echo "=> Project production dir:$REMOTE_PROJECT_PROD_DIR"
    - echo "=> Project release dir:$REMOTE_PROJECT_RELEASE_DIR"
    - LATEST_RELEASE_PATH="$REMOTE_PROJECT_RELEASE_DIR/$DEPLOY_TIME"
    - echo "=> Latest release dir:$LATEST_RELEASE_PATH"
    #
    - echo "============|| Build Deployment... ||============"
    - cp .env.$CI_ENVIRONMENT_NAME.example .env
    - yarn
    - yarn build
    #
    - echo "============|| Create Released Deployment Folder... ||============"
    - ssh -T -p $SSH_PORT $SSH_USER@$SSH_SERVER_IP "pwd; mkdir -p $LATEST_RELEASE_PATH"
    #
    - echo "============|| Starting deploy... ||============"
    - rsync -a -e "ssh -p $SSH_PORT" node_modules/ $SSH_USER@$SSH_SERVER_IP:$LATEST_RELEASE_PATH/node_modules
    - rsync -a -e "ssh -p $SSH_PORT" public/ $SSH_USER@$SSH_SERVER_IP:$LATEST_RELEASE_PATH/public
    - rsync -a -e "ssh -p $SSH_PORT" .next/ $SSH_USER@$SSH_SERVER_IP:$LATEST_RELEASE_PATH/.next
    - rsync -a -e "ssh -p $SSH_PORT" .env $SSH_USER@$SSH_SERVER_IP:$LATEST_RELEASE_PATH/.env
    - rsync -a -e "ssh -p $SSH_PORT" next.config.js $SSH_USER@$SSH_SERVER_IP:$LATEST_RELEASE_PATH/next.config.js
    - rsync -a -e "ssh -p $SSH_PORT" package.json $SSH_USER@$SSH_SERVER_IP:$LATEST_RELEASE_PATH/package.json
    #
    - echo "============|| Setup Rollback... ||============"
    - ssh -T -p $SSH_PORT $SSH_USER@$SSH_SERVER_IP "pwd; readlink -f $REMOTE_PROJECT_PROD_DIR"
    - ssh -T -p $SSH_PORT $SSH_USER@$SSH_SERVER_IP "pwd; touch $REMOTE_PROJECT_DIR/latest-rollback-path.txt; chmod -R 0777 $REMOTE_PROJECT_DIR/latest-rollback-path.txt; truncate -s 0 $REMOTE_PROJECT_DIR/latest-rollback-path.txt; readlink -f $REMOTE_PROJECT_PROD_DIR > $REMOTE_PROJECT_DIR/latest-rollback-path.txt"
    #
    - echo "============|| Linking Current Release... ||============"
    - echo $DEPLOY_TIME
    - echo $LATEST_RELEASE_PATH
    - echo $REMOTE_PROJECT_PROD_DIR
    - ssh -T -p $SSH_PORT $SSH_USER@$SSH_SERVER_IP "ln -nfs "$LATEST_RELEASE_PATH" "$REMOTE_PROJECT_PROD_DIR""
    - ssh -T -p $SSH_PORT $SSH_USER@$SSH_SERVER_IP "touch "$LATEST_RELEASE_PATH/release-$DEPLOY_TIME.note.txt""
    - ssh -T -p $SSH_PORT $SSH_USER@$SSH_SERVER_IP "echo "release_time at $DEPLOY_TIME on folder $LATEST_RELEASE_PATH" >> "$LATEST_RELEASE_PATH/release-$DEPLOY_TIME.note.txt""
    #
    - echo "============|| Restart server ... ||============"
    - ssh -T -p $SSH_PORT $SSH_USER@$SSH_SERVER_IP "pwd; source ~/.nvm/nvm.sh; cd $REMOTE_PROJECT_DIR; pm2 restart clout-frontend"
    #
    - echo "============|| Clean Release... ||============"
    - echo $REMOTE_PROJECT_RELEASE_DIR
    - ssh -T -p $SSH_PORT $SSH_USER@$SSH_SERVER_IP "pwd; cd $REMOTE_PROJECT_DIR; ls -la $REMOTE_PROJECT_RELEASE_DIR; ls -dt "$REMOTE_PROJECT_RELEASE_DIR"/*/ | tail -n +4 | xargs rm -rf; ls -la $REMOTE_PROJECT_RELEASE_DIR"
  # tags:
  #   - local_sv

.rollback_staging:
  stage: rollback_staging
  image:
    name: node:18-alpine
  before_script:
    - apk update && apk add openssh-client bash rsync yarn sshpass
    - apk --no-cache --virtual build-dependencies add python3 make g++
    - eval $(ssh-agent -s)
    - bash -c 'ssh-add <(echo "$SSH_PRIVATE_KEY")'
    - mkdir -p ~/.ssh
    - ssh-keyscan -H $SSH_SERVER_IP >> ~/.ssh/known_hosts
    - chmod 644 ~/.ssh/known_hosts
  script:
    #
    - echo "============|| Setup Environment... ||============"
    - REMOTE_PROJECT_DIR=$REMOTE_PROJECT_DIR
    - REMOTE_PROJECT_PROD_DIR=$REMOTE_PROJECT_PROD_DIR
    - REMOTE_PROJECT_ROLLBACK_DIR=$(ssh -T -p $SSH_PORT $SSH_USER@$SSH_SERVER_IP "cat $REMOTE_PROJECT_DIR/latest-rollback-path.txt")
    - echo "Last remote project rollback dir:$REMOTE_PROJECT_ROLLBACK_DIR"
    - LATEST_REMOTE_PROJECT_PROD_DIR=$(ssh -T -p $SSH_PORT $SSH_USER@$SSH_SERVER_IP "readlink -f $REMOTE_PROJECT_PROD_DIR")
    - echo "Current remote production dir:$LATEST_REMOTE_PROJECT_PROD_DIR"
    #
    - echo "============|| START ROLLBACK ||============"
    - ssh -T -p $SSH_PORT $SSH_USER@$SSH_SERVER_IP "pwd; ls -la $REMOTE_PROJECT_DIR; sudo ln -nfs "$REMOTE_PROJECT_ROLLBACK_DIR" "$REMOTE_PROJECT_PROD_DIR"; ls -la $REMOTE_PROJECT_RELEASE_DIR; rm -rf $LATEST_REMOTE_PROJECT_PROD_DIR; ls -la $REMOTE_PROJECT_RELEASE_DIR"
    #
    - echo "============|| Restart server ... ||============"
    - ssh -T -p $SSH_PORT $SSH_USER@$SSH_SERVER_IP "pwd; source ~/.nvm/nvm.sh; cd $REMOTE_PROJECT_DIR; pm2 restart clout-frontend"
  # tags:
  #   - local_sv

release_staging:
  extends: .release_staging
  rules: *push_changelog_rules
  environment:
    name: staging

rollback_staging:
  extends: .rollback_staging
  rules: *push_changelog_rules_manual
  environment:
    name: staging
