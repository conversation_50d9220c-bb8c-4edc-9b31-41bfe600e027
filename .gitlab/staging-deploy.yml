stages:
  - release_staging

# Define push changelog rules
.push_changelog_rules: &push_changelog_rules
  - if: '$CI_COMMIT_MESSAGE =~ /CHANGELOG\.md/i'
    when: never
  - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH || $CI_COMMIT_BRANCH == "staging"
    when: always
  - when: never
# Define push changelog rules
.push_changelog_rules_manual: &push_changelog_rules_manual
  - if: '$CI_COMMIT_MESSAGE =~ /CHANGELOG\.md/i'
    when: never
  - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH || $CI_COMMIT_BRANCH == "staging"
    when: manual
  - when: never

.release_staging:
  stage: release_staging
  image:
    name: node:18-alpine
  before_script:
    - apk update && apk add openssh-client bash rsync yarn sshpass
    - apk --no-cache --virtual build-dependencies add python3 make g++
    - eval $(ssh-agent -s)
    - bash -c 'ssh-add <(echo "$SSH_PRIVATE_KEY")'
    - mkdir -p ~/.ssh
    - ssh-keyscan -H $SSH_SERVER_IP >> ~/.ssh/known_hosts
    - chmod 644 ~/.ssh/known_hosts
  script:
    - rm -f .env
    - echo "$ENV" > .env
    - yarn
    - yarn build

    - ssh -T $SSH_USER@$SSH_SERVER_IP "pwd; cd $REMOTE_PROJECT_DIR; git pull;"

    - rsync -a -e 'ssh' node_modules/ $SSH_USER@$SSH_SERVER_IP:$REMOTE_PROJECT_DIR/node_modules
    - rsync -a -e 'ssh' .env $SSH_USER@$SSH_SERVER_IP:$REMOTE_PROJECT_DIR/.env
    - rsync -a -e 'ssh' .next/ $SSH_USER@$SSH_SERVER_IP:$REMOTE_PROJECT_DIR/.next

    - ssh -T $SSH_USER@$SSH_SERVER_IP "pwd; source ~/.nvm/nvm.sh; pm2 restart clout-frontend;"

release_staging:
  extends: .release_staging
  rules: *push_changelog_rules
  environment:
    name: staging
