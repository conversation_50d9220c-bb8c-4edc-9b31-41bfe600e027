stages:
  - reload_docker_develop

.reload-docker-compose-develop:
  stage: reload_docker_develop
  image:
    name: alpine:latest
  before_script:
    - apk update && apk add openssh-client bash rsync yarn sshpass
    - apk --no-cache --virtual build-dependencies add python3 make g++
    - eval $(ssh-agent -s)
    - mkdir -p ~/.ssh
    - ssh-keyscan -H $SSH_SERVER_IP >> ~/.ssh/known_hosts
    - chmod 644 ~/.ssh/known_hosts
  script:
    - pwd
    - echo "$ENV" > .env
    - sshpass -p $SSH_PASSWORD rsync -avz .env $SSH_USER@$SSH_SERVER_IP:$REMOTE_PROJECT_DIR/.env
    - sshpass -p $SSH_PASSWORD ssh -T $SSH_USER@$SSH_SERVER_IP "pwd; cd $REMOTE_PROJECT_DIR; git pull; docker compose build; docker compose up -d; docker system prune -f"
  tags:
    - local_sv

reload-docker-compose-develop:
  extends: .reload-docker-compose-develop
  only:
    refs:
      - develop
  environment:
    name: develop
