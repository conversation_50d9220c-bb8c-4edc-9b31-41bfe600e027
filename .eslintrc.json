{
  "extends": [
    "next/core-web-vitals",
    "airbnb",
    "airbnb-typescript",
    "plugin:@typescript-eslint/recommended",
    "prettier"
  ],
  "parserOptions": {
    "project": "./tsconfig.json"
  },
  "plugins": ["@typescript-eslint", "prettier"],
  "rules": {
    "prettier/prettier": ["error"],
    "no-param-reassign": ["error", { "props": false }],
    "@typescript-eslint/no-unused-vars": "error",
    "@typescript-eslint/lines-between-class-members": "off",
    // Possible errors
    "max-lines-per-function": ["error", { "max": 200, "skipBlankLines": true, "skipComments": true }],
    "max-lines": ["error", { "max": 300, "skipBlankLines": true, "skipComments": true }],
    "no-console": "error",
    "react/function-component-definition": [
      "error",
      {
        "namedComponents": ["function-declaration", "arrow-function"]
      }
    ],
    "react/jsx-props-no-spreading": "off",
    "@typescript-eslint/no-empty-function": "off",
    // Best practices
    "dot-notation": "error",
    "no-else-return": "error",
    "no-floating-decimal": "error",
    "no-sequences": "error",
    // Stylistic
    "array-bracket-spacing": "error",
    "computed-property-spacing": ["error", "never"],
    "curly": "error",
    "no-lonely-if": "error",
    "no-unneeded-ternary": "error",
    "one-var-declaration-per-line": "error",
    "quotes": [
      "error",
      "single",
      {
        "allowTemplateLiterals": false,
        "avoidEscape": true
      }
    ],
    // ES6
    "array-callback-return": "off",
    "prefer-const": "error",
    // Imports
    "import/prefer-default-export": "off",
    "sort-imports": [
      "error",
      {
        "ignoreCase": true,
        "ignoreDeclarationSort": true
      }
    ],
    "no-unused-expressions": "off",
    "no-prototype-builtins": "off",
    // REACT
    "react/jsx-uses-react": "off",
    "react/react-in-jsx-scope": "off",
    "jsx-a11y/href-no-hash": [0],
    "react/display-name": 0,
    "react/no-deprecated": "error",
    "react/no-unsafe": [
      "error",
      {
        "checkAliases": true
      }
    ],
    "react/jsx-sort-props": [
      "error",
      {
        "ignoreCase": true
      }
    ],
    "react-hooks/rules-of-hooks": "error",
    "react-hooks/exhaustive-deps": 0
  }
}
