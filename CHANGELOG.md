**Changelog updates:** 🔄

## 2024-06-11

### Fixed
- Fixed CI/CD pipeline issues in GitLab configuration and Docker image installation script.


>to commit the new content to the CHANGELOG.md file, please type:
>'/update_changelog --pr_update_changelog.push_changelog_changes=true'
- fix cicd from fix-cicd to staging by [@khanh99](http://gitlab.lisod.vn/khanh99) in [!354](http://gitlab.lisod.vn/1_ongoing1/clout/-/merge_requests/354)
null
- Update/task 19 from update/task_19 to staging by [@letrunghieu0803](http://gitlab.lisod.vn/letrunghieu0803) in [!356](http://gitlab.lisod.vn/1_ongoing1/clout/-/merge_requests/356)
null
- fix text from update/task_19 to staging by [@letrunghieu0803](http://gitlab.lisod.vn/letrunghieu0803) in [!357](http://gitlab.lisod.vn/1_ongoing1/clout/-/merge_requests/357)
null
- update redirect task 19 from update/task_19 to staging by [@letrunghieu0803](http://gitlab.lisod.vn/letrunghieu0803) in [!358](http://gitlab.lisod.vn/1_ongoing1/clout/-/merge_requests/358)
null
- update fix text from update/task_19 to staging by [@letrunghieu0803](http://gitlab.lisod.vn/letrunghieu0803) in [!359](http://gitlab.lisod.vn/1_ongoing1/clout/-/merge_requests/359)
**Changelog updates:** 🔄

## 2024-06-12

### Changed
- Updated CI script to ensure dependencies are installed.


>to commit the new content to the CHANGELOG.md file, please type:
>'/update_changelog --pr_update_changelog.push_changelog_changes=true'
- install dependencies ci from install_dependences_ci to staging by [@khanh99](http://gitlab.lisod.vn/khanh99) in [!360](http://gitlab.lisod.vn/1_ongoing1/clout/-/merge_requests/360)
null
- update task 19 from fix-bug/task-19 to staging by [@letrunghieu0803](http://gitlab.lisod.vn/letrunghieu0803) in [!362](http://gitlab.lisod.vn/1_ongoing1/clout/-/merge_requests/362)
null
- fix task 12 from fix-bug/task-1-2 to staging by [@letrunghieu0803](http://gitlab.lisod.vn/letrunghieu0803) in [!363](http://gitlab.lisod.vn/1_ongoing1/clout/-/merge_requests/363)
