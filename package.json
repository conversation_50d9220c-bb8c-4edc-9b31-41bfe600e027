{"name": "clout", "version": "0.1.0", "private": true, "scripts": {"dev": "set NODE_OPTIONS=--require=elastic-apm-node/start-next.js && next dev", "build": "next build", "start": "set NODE_OPTIONS=--require=elastic-apm-node/start-next.js && next start", "lint": "next lint", "postinstall": "husky install"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@elastic/apm-rum": "^5.16.0", "@hookform/error-message": "^2.0.1", "@hookform/resolvers": "^3.3.2", "@reduxjs/toolkit": "^2.0.1", "@square/web-payments-sdk-types": "^1.54.4", "@uidotdev/usehooks": "^2.4.1", "antd": "^5.11.1", "antd-img-crop": "^4.18.0", "async-mutex": "^0.4.0", "axios": "^1.6.2", "clsx": "^2.0.0", "dayjs": "^1.11.10", "dotenv": "^16.3.1", "elastic-apm-node": "^4.0.0", "eslint-config-airbnb": "^19.0.4", "lodash": "^4.17.21", "lodash.debounce": "^4.0.8", "moment": "^2.30.0", "next": "14.0.2", "next-redux-wrapper": "^8.1.0", "react": "^18", "react-copy-to-clipboard": "^5.1.0", "react-device-detect": "^2.2.3", "react-dom": "^18", "react-easy-crop": "^5.0.3", "react-google-recaptcha": "^3.1.0", "react-hook-form": "^7.48.2", "react-otp-input": "^3.1.1", "react-quill": "^2.0.0", "react-redux": "^9.0.2", "react-square-web-payments-sdk": "^3.2.1", "recharts": "^2.12.1", "redux-persist": "^6.0.0", "square": "^33.1.0", "usehooks-ts": "^2.9.5", "uuid": "^9.0.1", "wanakana": "^5.3.1", "yup": "^1.3.2"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-csv": "^1.1.10", "@types/react-dom": "^18", "@types/react-google-recaptcha": "^2.1.8", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "autoprefixer": "^10.0.1", "eslint": "^8.53.0", "eslint-config-airbnb-typescript": "^17.1.0", "eslint-config-next": "14.0.2", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.1", "husky": "^8.0.3", "next-transpile-modules": "^10.0.1", "postcss": "^8", "prettier": "^3.1.0", "sass": "^1.69.5", "tailwindcss": "^3.3.0", "typescript": "^5"}}