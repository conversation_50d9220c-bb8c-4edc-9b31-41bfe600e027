/* eslint-disable @typescript-eslint/no-explicit-any */
import { api } from '../api';

export interface BannerData {
  device?: string | null | undefined;
  id?: number | null | undefined;
  location?: string | null | undefined;
  type?: string | null | undefined;
  images?: {
    alt_image?: string | null | undefined;
    bannerId?: number | null | undefined;
    id?: number | null | undefined;
    createdAt?: string | null | undefined;
    uploadAt?: string | null | undefined;
    updatedAt?: string | null | undefined;
    imageUrl?: string | null | undefined;
    url?: string | null | undefined;
    dynamic_url?: string | null | undefined;
  }[];
}

interface TypeId {
  id?: string | string[] | undefined;
}

export interface TypeBannerResponse {
  data: BannerData[];
  status?: string | null | undefined;
}
interface TypeBannerDetailResponse {
  data: BannerData;
  status?: string | null | undefined;
}

export const bannerApi = api.injectEndpoints({
  endpoints: (build) => ({
    createBanner: build.mutation<any, FormData>({
      query: (queryArg) => ({
        url: '/banners',
        method: 'POST',
        body: queryArg,
      }),
    }),
    getBanner: build.query<TypeBannerResponse, object>({
      query: (queryArg) => ({
        url: '/banners',
        method: 'GET',
        params: queryArg,
      }),
    }),
    getDetailBanner: build.query<TypeBannerDetailResponse, TypeId>({
      query: (queryArg) => ({
        url: `/banners/${queryArg?.id}`,
        method: 'GET',
      }),
      keepUnusedDataFor: 0,
    }),
    updateBanner: build.mutation<any, { dataSent: FormData; id: TypeId['id'] }>({
      query: (queryArg) => ({
        url: `/banners/${queryArg?.id}`,
        method: 'PUT',
        body: queryArg?.dataSent,
      }),
    }),
  }),
});

export const { useCreateBannerMutation, useGetBannerQuery, useGetDetailBannerQuery, useUpdateBannerMutation } =
  bannerApi;
