/* eslint-disable @typescript-eslint/no-explicit-any */
import { BaseQueryFn, EndpointBuilder } from '@reduxjs/toolkit/query';

export default function emailSendOtp(build: EndpointBuilder<BaseQueryFn, string, string>) {
  return build.mutation<
    any,
    {
      email: string;
      userId?: number;
    }
  >({
    query(body) {
      return {
        url: '/auth/verify_mail',
        method: 'POST',
        body,
      };
    },
  });
}
