/* eslint-disable @typescript-eslint/no-explicit-any */
import { api } from '../api';

const injectedRtkApi = api.injectEndpoints({
  endpoints: (build) => ({
    getMaintenance: build.query<ResponseGetListMaintenance, any>({
      query: (queryArg) => ({
        url: '/maintenance-periods',
        method: 'GET',
        params: queryArg,
      }),
      providesTags: ['Maintenance'],
    }),
    createMaintenance: build.mutation<any, CreateMaintenanceBody>({
      query: (queryArg) => ({
        url: '/maintenance-periods',
        method: 'POST',
        body: queryArg,
      }),
      invalidatesTags: ['Maintenance'],
    }),
    updateMaintenance: build.mutation<MaintenanceResponse, UpdateMaintenanceParams>({
      query: (queryArg) => ({
        url: `/maintenance-periods/${queryArg.id}`,
        method: 'PUT',
        body: queryArg,
      }),
      invalidatesTags: ['Maintenance'],
    }),
    deleteMaintenance: build.mutation<MaintenanceResponse, DeleteMaintenanceParams>({
      query: (queryArg) => ({
        url: `/maintenance-periods/${queryArg.id}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['Maintenance'],
    }),
  }),
});

export type MaintenanceResponse = {
  data: any;
  tasks: [
    {
      id: number;
      campaignId: string;
      type: string;
      taskActionType: string;
      taskTemplateId: number;
      updatedAt: string;
      createdAt: string;
      taskTemplate: {
        id: number;
        userName: string;
        extra: string | null;
        link: string;
        quote: string | null;
        required: boolean;
        updatedAt: string;
        createdAt: string;
      };
    },
  ];
  total: number;
};
export interface ResponseGetListMaintenance {
  data: DataMaintenance;
  status: string;
}
export interface DataMaintenance {
  data: DataMaintenanceItem[];
  total: number;
}

export interface DataMaintenanceItem {
  id: number;
  startAt: Date;
  endAt: Date;
  description: string;
  status: string;
  updatedAt: Date;
  deleteAt: null;
  createdAt: Date;
}

export type CreateMaintenanceBody = {
  startAt: string;
  endAt: string;
  description?: string;
  status?: string;
};

export type UpdateMaintenanceParams = {
  id: number;
  startAt: string;
  endAt: string;
  description?: string;
  status?: string;
};
export type DeleteMaintenanceParams = {
  id: number;
};
export type MaintenanceParams = {
  campaignId: string;
  data: {
    taskId?: string;
    type: string;
    taskActionType?: string;
    taskTemplate: {
      taskTemplateId?: string;
      userName: string;
      link: string;
      config: any;
    };
    isRequiredMaintenance?: boolean;
    pointsAwarded?: string;
  }[];
};

export { injectedRtkApi as MaintenanceApi };
export const {
  useCreateMaintenanceMutation,
  useDeleteMaintenanceMutation,
  useGetMaintenanceQuery,
  useLazyGetMaintenanceQuery,
  useUpdateMaintenanceMutation,
} = injectedRtkApi;
