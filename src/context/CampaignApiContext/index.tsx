/* eslint-disable max-lines */
/* eslint-disable max-lines-per-function */
import { useLazyMeQuery } from '@/redux/endpoints/auth';
import {
  TypeCampaign,
  TypeCampaignReward,
  TypeTask,
  useDeleteCampaignMutation,
  useLazyGetDetailCampaignQuery,
  usePostQuestsMutation,
  useUpdateCampaignMutation,
} from '@/redux/endpoints/campaign';
import { usePostPaymentMutation } from '@/redux/endpoints/payment';
import {
  useDeleteReWardsMutation,
  useLazyGetReWardsQuery,
  usePostReWardsMutation,
  useUpdateReWardsMutation,
} from '@/redux/endpoints/reWard';
import {
  useDeleteTaskMutation,
  useLazyGetTasksQuery,
  usePostTaskMutation,
  useUpdateTaskMutation,
} from '@/redux/endpoints/task';
import { RootState } from '@/redux/store';
import { TypeResponseFormCampaign } from '@/types/campaign.type';
import { CampaignFormName } from '@/utils/constant/enums';
import adapterCampaignParams, {
  adapterDataReWard,
  adapterDataTask,
  adapterNewTask,
} from '@/utils/func/adapterCampaignParams';
import { getErrorMessage } from '@/utils/func/getErrorMessage';
import toastMessage from '@/utils/func/toastMessage';
import { useRouter } from 'next/router';
import React, { createContext, useCallback, useContext, useEffect, useMemo, useState } from 'react';
import { useSelector } from 'react-redux';

export type TypeCampaignContext = {
  taskIdDeletes: number[];
  setTaskIdDeletes?: React.Dispatch<React.SetStateAction<number[]>>;
  setReWardIdDelete?: React.Dispatch<React.SetStateAction<number[]>>;
  handleDeleteCampaign?: (campaignId: string | number) => void;

  handleCreateCampaignV1?: (
    queryParams: TypeResponseFormCampaign,
    type: 'DRAFT' | 'WAITING_FOR_PURCASE' | 'UNDER_REVIEW' | 'WAITING_FOR_PUBLICATION' | 'PUBLIC' | 'COMPLETION',
    formName: CampaignFormName,
    isValidFileds: boolean
  ) => Promise<void>;
  handleUpdateCampaignV1?: (
    campaignId: string,
    queryParams: TypeResponseFormCampaign,
    type: 'DRAFT' | 'WAITING_FOR_PURCASE' | 'UNDER_REVIEW' | 'WAITING_FOR_PUBLICATION' | 'PUBLIC' | 'COMPLETION',
    formName: CampaignFormName,
    isValidFileds: boolean,
    method?: 'CREATE'
  ) => Promise<void>;
  dataCampaignInfo: TypeCampaign | undefined;
  dataCampaignDetailReward:
    | {
        rewards: TypeCampaignReward[];
        total: number;
      }
    | undefined;
  dataCampaignDetailTask:
    | {
        tasks: TypeTask[];
        total: number;
      }
    | undefined;
  isFetchingInitData: boolean;
  isValidUserLogged: boolean;
};

const CampaignApiContext = createContext<TypeCampaignContext>({
  taskIdDeletes: [],
  dataCampaignInfo: undefined,
  dataCampaignDetailReward: undefined,
  dataCampaignDetailTask: undefined,
  isFetchingInitData: false,
  isValidUserLogged: false,
});
export const CampaignApiProvider = ({ children }: { children: React.ReactNode }) => {
  const router = useRouter();

  const [campaignDetailData, setCampaignDetailData] = useState<TypeCampaign | undefined>(undefined);

  const [campaignDetailReward, setCampaignDetailReward] = useState<
    | {
        rewards: TypeCampaignReward[];
        total: number;
      }
    | undefined
  >(undefined);

  const [campaignDetailTask, setCampaignDetailTask] = useState<
    | {
        tasks: TypeTask[];
        total: number;
      }
    | undefined
  >(undefined);

  const [triggerGetCampaignInfo] = useLazyGetDetailCampaignQuery();
  const [triggerGetCampaignTask] = useLazyGetTasksQuery();
  const [triggerGetCampaignReward] = useLazyGetReWardsQuery();

  const { accessToken, user } = useSelector((state: RootState) => state.auth);

  const [taskIdDeletes, setTaskIdDeletes] = useState<number[]>([]);
  const [reWardIdDelete, setReWardIdDelete] = useState<number[]>([]);

  const [triggerMe] = useLazyMeQuery();
  // CREATE
  const [createCampaign] = usePostQuestsMutation();
  const [createTask] = usePostTaskMutation();
  const [createReWard] = usePostReWardsMutation();

  // UPDATE
  const [updateCampaign] = useUpdateCampaignMutation();
  const [updateTask] = useUpdateTaskMutation();
  const [updateReWard] = useUpdateReWardsMutation();

  // DELETE
  const [deleteCampaign] = useDeleteCampaignMutation();
  const [deleteTask] = useDeleteTaskMutation();
  const [deleteReWard] = useDeleteReWardsMutation();

  // PAYMENT
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [createPayment] = usePostPaymentMutation();

  const [isFetchingInitData, setIsFetchingInitData] = useState(false);
  const [isValidUserLogged, setIsValidUserLogged] = useState(true);

  const handleGetCampaignInfoData = async () => {
    try {
      const campaignId = router?.query?.id;
      if (campaignId) {
        setIsValidUserLogged(true);
        if (
          accessToken &&
          campaignId &&
          typeof campaignId === 'string' &&
          campaignId &&
          campaignId !== 'null' &&
          campaignId !== 'undefined'
        ) {
          setIsFetchingInitData(true);
          const dataInfo = await triggerGetCampaignInfo(
            {
              campaignId,
              isAdmin: true,
            },
            false
          ).unwrap();
          if (dataInfo) {
            setCampaignDetailData(dataInfo);
          }
          const dataCampaignTask = await triggerGetCampaignTask(
            {
              campaignId,
            },
            false
          ).unwrap();
          if (dataCampaignTask) {
            setCampaignDetailTask(dataCampaignTask);
          }
          const dataCampaignReward = await triggerGetCampaignReward(
            {
              campaignId,
            },
            false
          ).unwrap();
          if (dataCampaignReward) {
            setCampaignDetailReward(dataCampaignReward);
          }
        } else {
          setCampaignDetailData(undefined);
          setCampaignDetailTask(undefined);
          setCampaignDetailReward(undefined);
        }
      } else {
        setCampaignDetailTask(undefined);
      }
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
    } catch (error: any) {
      // eslint-disable-next-line no-console
      console.log('get error', error);
      if (error?.status === 401) {
        setIsValidUserLogged(false);
      }
    } finally {
      setIsFetchingInitData(false);
    }
  };

  useEffect(() => {
    handleGetCampaignInfoData();
  }, [accessToken, router]);

  const handleCreateCampaignV1 = async (
    queryParams: TypeResponseFormCampaign,
    type: 'DRAFT' | 'WAITING_FOR_PURCASE' | 'UNDER_REVIEW' | 'WAITING_FOR_PUBLICATION' | 'PUBLIC' | 'COMPLETION',
    formName: CampaignFormName,
    isValidFileds: boolean
  ) => {
    try {
      const queryParamsClone = {
        ...queryParams,
      };

      if (queryParams.typeWinner === 'AUTO_PRIZEE_DRAW') {
        queryParamsClone.compensationSummary = '';
      } else {
        queryParamsClone.reWard = undefined;
      }

      const campaignCreated = await createCampaign(
        adapterCampaignParams(
          queryParamsClone,
          queryParamsClone.typeWinner,
          type === 'UNDER_REVIEW' ? 'DRAFT' : type,
          isValidFileds
        )
      ).unwrap();
      if (campaignCreated?.newCampaign?.id) {
        const campaignTaskCreated = await createTask({
          campaignId: campaignCreated?.newCampaign?.id ?? '',
          data: adapterDataTask(queryParamsClone),
        }).unwrap();
        const campaignRewardCreated = await createReWard({
          campaignId: campaignCreated?.newCampaign?.id ?? '',
          data: adapterDataReWard(queryParamsClone),
        }).unwrap();
        if (campaignTaskCreated && campaignRewardCreated) {
          if (type === 'UNDER_REVIEW') {
            await createPayment({
              campaignId: campaignCreated?.newCampaign?.id ?? '',
              price: Number(queryParamsClone.price),
              priceWithTax: Number(queryParamsClone.priceWithTax),
              pointUse: Number(queryParamsClone.depositBalance ?? 0),
              // usePoint: queryParams?.usePoint ?? false,
            }).unwrap();

            triggerMe();
            if (formName === 'CAMPAIGN_CONFIRMATION') {
              toastMessage('ステータスを更新しました。', 'success');
            }
            router.push('/campaign-creator/list');
          } else {
            switch (formName) {
              case 'CAMPAIGN_CONFIRMATION':
              case 'CAMPAIGN_DELETE':
              case 'CAMPAIGN_SAVE_DRAFT': {
                if (formName === 'CAMPAIGN_CONFIRMATION') {
                  toastMessage('キャンペーンが作成されました。順次公開されます。', 'success');
                }
                if (formName === 'CAMPAIGN_SAVE_DRAFT') {
                  toastMessage('下書き保存に成功しました。', 'success');
                }
                router.push('/campaign-creator/list');
                break;
              }
              case 'CAMPAIGN_SETUP_INFO': {
                if (router.pathname.startsWith('/campaign-creator/edit')) {
                  router.push(`/campaign-creator/edit/${campaignCreated?.newCampaign?.id}?step=2`);
                } else {
                  router.push(`/campaign-creator/create/draft/${campaignCreated?.newCampaign?.id}?step=2`);
                }
                break;
              }
              // case 'CAMPAIGN_PREVIEW': {
              //   const url = `${window.location.origin}/campaigns/${campaignCreated?.newCampaign?.id}`;
              //   copyFunc(url);
              //   router.push(`/campaign-creator/create/draft/${campaignCreated?.newCampaign?.id}`);
              //   break;
              // }
              case 'CAMPAIGN_SETUP_TASKS': {
                if (router.pathname.startsWith('/campaign-creator/edit')) {
                  toastMessage('正常に保存されました。', 'success');
                  // router.push(`/campaign-creator/edit/${campaignCreated?.newCampaign?.id}?step=3`);
                } else {
                  router.push(`/campaign-creator/create/draft/${campaignCreated?.newCampaign?.id}?step=3`);
                }
                break;
              }
              case 'CAMPAIGN_SETUP_REWARDS': {
                if (user?.companyRole?.membership !== 'MANAGER') {
                  router.push('/campaign-creator/list');
                } else if (router.pathname.startsWith('/campaign-creator/edit')) {
                  router.push(`/campaign-creator/edit/${campaignCreated?.newCampaign?.id}?step=4`);
                } else {
                  router.push(`/campaign-creator/create/draft/${campaignCreated?.newCampaign?.id}?step=4`);
                }
                break;
              }
              default:
                break;
            }
          }
        }
      }
    } catch (err) {
      toastMessage(getErrorMessage(err), 'error');
    }
  };

  const handleUpdateCampaignV1 = async (
    campaignId: string,
    queryParams: TypeResponseFormCampaign,
    type: 'DRAFT' | 'WAITING_FOR_PURCASE' | 'UNDER_REVIEW' | 'WAITING_FOR_PUBLICATION' | 'PUBLIC' | 'COMPLETION',
    formName: CampaignFormName,
    isValidFileds: boolean,
    method?: 'CREATE'
  ) => {
    try {
      const queryParamsClone = {
        ...queryParams,
      };
      let arrRewardIdRemove: number[] = [];
      const optionTask = { ...queryParamsClone.optionTasks };
      if (queryParams.typeWinner === 'AUTO_PRIZEE_DRAW') {
        queryParamsClone.compensationSummary = '';
        const invalidShareUrlTask = Object.values(optionTask ?? {}).find((item) => item.platForm === 'SHARE_URL');

        if (invalidShareUrlTask && Number(queryParams?.reWard?.reWard1?.money || 0) > 0) {
          toastMessage(
            'インスタントウィンを使用する場合、URLから友達を招待するタスクはご利用いただけません。',
            'error'
          );
          // console.log('router.pathname', window.location.href);
          if (formName !== 'CAMPAIGN_SETUP_REWARDS') {
            router.push(`${window.location.href}?step=2`);
          }
          return;
        }
      } else {
        queryParamsClone.reWard = undefined;
        if (campaignDetailReward?.rewards?.length) {
          arrRewardIdRemove = campaignDetailReward?.rewards?.map((i) => Number(i.id));
        }
      }

      const campaignUpdated = await updateCampaign({
        campaignId,
        body: adapterCampaignParams(queryParamsClone, queryParamsClone.typeWinner, type, isValidFileds),
      }).unwrap();
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      const { newPoints, taskShareUrl } = queryParamsClone;
      if (newPoints) {
        // eslint-disable-next-line no-restricted-syntax, guard-for-in
        for (const taskKey in optionTask) {
          const taskId = optionTask[taskKey]?.taskId?.toString();

          if (taskId && newPoints?.hasOwnProperty(taskId)) {
            optionTask[taskKey].points = newPoints?.[taskId]?.points;
          }
        }
        queryParamsClone.optionTasks = { ...optionTask };
      }
      const hasTaskRequired = Object.values(optionTask ?? {}).find((item) => item.required === true);
      if (!hasTaskRequired) {
        toastMessage('必須タスクを一つ以上設定してください', 'error');
        if (router.pathname.startsWith('/campaign-creator/edit')) {
          router.push(`/campaign-creator/edit/${campaignUpdated?.newCampaign?.id}?step=2`);
        } else {
          router.push(`/campaign-creator/create/draft/${campaignUpdated?.newCampaign?.id}?step=2`);
        }
        return;
      }
      // if (formName === 'CAMPAIGN_SETUP_REWARDS') {
      //   if (taskShareUrl && taskShareUrl.required) {
      //     const lengthTasks = Object.keys(queryParamsClone?.optionTasks ?? {}).length + 1;
      //     const invalidShareUrlTask = Object.values(queryParamsClone?.optionTasks ?? {}).findIndex(
      //       (item) => item.platForm === 'SHARE_URL'
      //     );
      //     queryParamsClone.optionTasks = {
      //       ...queryParamsClone.optionTasks,
      //       [invalidShareUrlTask > -1
      //         ? Object.keys(queryParamsClone?.optionTasks ?? {})[invalidShareUrlTask]
      //         : `task${lengthTasks}`]: {
      //         taskId:
      //           invalidShareUrlTask > -1
      //             ? Object.values(queryParamsClone?.optionTasks ?? {})[invalidShareUrlTask]?.taskId
      //             : undefined,
      //         platForm: 'SHARE_URL',
      //         required: false,
      //         points: taskShareUrl.points,
      //         type: 'NONE',
      //       },
      //     };
      //     if (invalidShareUrlTask > -1) {
      //       const keyToRemove = Object.keys(queryParamsClone?.optionTasks ?? {})[invalidShareUrlTask];
      //       delete queryParamsClone?.optionTasks?.[keyToRemove];
      //     }
      //   } else {
      //     const invalidShareUrlTask = Object.values(queryParamsClone?.optionTasks ?? {}).find(
      //       (item) => item.platForm === 'SHARE_URL'
      //     );
      //     if (invalidShareUrlTask && invalidShareUrlTask.taskId) {
      //       await deleteTask({
      //         campaignId: campaignUpdated?.newCampaign?.id ?? '',
      //         taskIds: [Number(invalidShareUrlTask.taskId)],
      //       }).unwrap();
      //       const indexShareUrlTask = Object.values(queryParamsClone?.optionTasks ?? {}).findIndex(
      //         (item) => item.platForm === 'SHARE_URL'
      //       );
      //       const keyToRemove = Object.keys(queryParamsClone?.optionTasks ?? {})[indexShareUrlTask];
      //       delete queryParamsClone?.optionTasks?.[keyToRemove];
      //     }
      //   }
      // }
      if (campaignUpdated?.newCampaign?.id) {
        const newTask = adapterNewTask(queryParamsClone);
        const newReward = adapterDataReWard(queryParamsClone).filter((e) => !e.rewardId);

        if (newTask.length > 0) {
          await createTask({
            campaignId: campaignUpdated?.newCampaign?.id ?? '',
            data: newTask,
          }).unwrap();
        }
        if (newReward.length > 0) {
          await createReWard({ campaignId: campaignUpdated?.newCampaign?.id ?? '', data: newReward }).unwrap();
        }
        if (taskIdDeletes.length > 0) {
          deleteTask({ campaignId: campaignUpdated?.newCampaign?.id ?? '', taskIds: taskIdDeletes })
            .unwrap()
            .finally(() => setTaskIdDeletes([]));
        }
        if (reWardIdDelete.length > 0 || arrRewardIdRemove?.length > 0) {
          deleteReWard({
            campaignId: campaignUpdated?.newCampaign?.id ?? '',
            rewardIds: [...new Set([...reWardIdDelete, ...arrRewardIdRemove])],
          })
            .unwrap()
            .finally(() => setReWardIdDelete([]));
        }
        const dataTask = await updateTask({
          campaignId: campaignUpdated?.newCampaign?.id ?? '',
          data: adapterDataTask(queryParamsClone).filter((e) => e.taskId),
        }).unwrap();
        const dataReward = await updateReWard({
          campaignId: campaignUpdated?.newCampaign?.id ?? '',
          data: adapterDataReWard(queryParamsClone).filter((e) => e.rewardId),
        }).unwrap();
        if (dataTask && dataReward) {
          if (method === 'CREATE') {
            await createPayment({
              campaignId: campaignUpdated?.newCampaign?.id ?? '',
              price: Number(queryParamsClone.price),
              priceWithTax: Number(queryParamsClone.priceWithTax),
              pointUse: Number(queryParamsClone.depositBalance ?? 0),
              // usePoint: queryParams?.usePoint ?? false,
            }).unwrap();
            if (formName === 'CAMPAIGN_CONFIRMATION') {
              toastMessage('ステータスを更新しました。', 'success');
            }
            router.push('/campaign-creator/list');
          } else {
            switch (formName) {
              case 'CAMPAIGN_CONFIRMATION':
              case 'CAMPAIGN_DELETE':
              case 'CAMPAIGN_SAVE_DRAFT': {
                if (formName === 'CAMPAIGN_SAVE_DRAFT') {
                  toastMessage('下書き保存に成功しました。', 'success');
                }
                if (formName === 'CAMPAIGN_CONFIRMATION') {
                  toastMessage('キャンペーンが作成されました。順次公開されます。', 'success');
                }
                router.push('/campaign-creator/list');
                break;
              }
              case 'CAMPAIGN_SETUP_INFO': {
                if (router.pathname.startsWith('/campaign-creator/edit')) {
                  router.push(`/campaign-creator/edit/${campaignUpdated?.newCampaign?.id}?step=2`);
                } else {
                  router.push(`/campaign-creator/create/draft/${campaignUpdated?.newCampaign?.id}?step=2`);
                }
                break;
              }
              case 'CAMPAIGN_SETUP_TASKS': {
                if (router.pathname.startsWith('/campaign-creator/edit')) {
                  toastMessage('正常に保存されました。', 'success');
                  // router.push(`/campaign-creator/edit/${campaignUpdated?.newCampaign?.id}?step=3`);
                } else {
                  router.push(`/campaign-creator/create/draft/${campaignUpdated?.newCampaign?.id}?step=3`);
                }
                break;
              }
              case 'CAMPAIGN_SETUP_REWARDS': {
                if (user?.companyRole?.membership !== 'MANAGER') {
                  router.push('/campaign-creator/list');
                } else if (router.pathname.startsWith('/campaign-creator/edit')) {
                  router.push(`/campaign-creator/edit/${campaignUpdated?.newCampaign?.id}?step=4`);
                } else {
                  router.push(`/campaign-creator/create/draft/${campaignUpdated?.newCampaign?.id}?step=4`);
                }
                break;
              }
              default:
                break;
            }
          }
        }
      }
    } catch (err) {
      toastMessage(getErrorMessage(err), 'error');
    } finally {
      await handleGetCampaignInfoData();
    }
  };

  const handleDeleteCampaign = useCallback((campaignId) => {
    deleteCampaign({ campaignId });
  }, []);

  const contextvalue = useMemo<TypeCampaignContext>(
    () => ({
      setTaskIdDeletes,
      setReWardIdDelete,

      handleDeleteCampaign,
      taskIdDeletes,
      handleCreateCampaignV1,
      handleUpdateCampaignV1,
      dataCampaignInfo: campaignDetailData,
      dataCampaignDetailReward: campaignDetailReward,
      dataCampaignDetailTask: campaignDetailTask,
      isFetchingInitData,
      isValidUserLogged,
    }),
    [
      setTaskIdDeletes,
      setReWardIdDelete,
      taskIdDeletes,
      reWardIdDelete,
      campaignDetailData,
      campaignDetailReward,
      campaignDetailTask,
      isFetchingInitData,
      isValidUserLogged,
    ]
  );
  return <CampaignApiContext.Provider value={contextvalue}>{children}</CampaignApiContext.Provider>;
};
export const useCampaignApiContext = () => {
  const popups = useContext(CampaignApiContext);

  return popups;
};
export default CampaignApiContext;
