@tailwind base;
@tailwind components;
@tailwind utilities;
@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+JP:wght@100;200;300;400;500;600;700;800;900&display=swap');

:root {
  font-family: 'Noto Sans JP', sans-serif;
  --font-noto-san-jp: 'Noto Sans JP', sans-serif;
  --font-montserrat: 'Montserrat', sans-serif;
  --main-header-height-mobile: 64px;
  --main-header-height-pc: 85px;
  --main-footer-height-mobile: 389px;
}
input[type='number'] {
  -moz-appearance: textfield;
}
input[type='number']::-webkit-outer-spin-button,
input[type='number']::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.ant-popconfirm-buttons .ant-btn-primary {
  background-color: #1677ff;
}

body {
  color: rgb(var(--foreground-rgb));
  background: linear-gradient(to bottom, transparent, rgb(var(--background-end-rgb))) rgb(var(--background-start-rgb));
  color: #333;
}

@layer base {
  html,
  body,
  div,
  span {
    font-family: var(--font-noto-san-jp), sans-serif;
  }
}

.stop-scrolling {
  overflow: hidden !important;
}

.custom-scroll::-webkit-scrollbar {
  width: 5px;
}
.custom-scroll::-webkit-scrollbar-track {
  background: #fff;
}
.custom-scroll::-webkit-scrollbar-thumb {
  background: #c6c6c6;
  border-radius: 5px;
}
.custom-scroll::-webkit-scrollbar-thumb:hover {
  background: #a9a9a9;
}

@keyframes animate1 {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

@keyframes animate2 {
  0% {
    transform: translateY(-100%);
  }
  100% {
    transform: translateY(100%);
  }
}

@keyframes animate3 {
  0% {
    transform: translateX(100%);
  }
  100% {
    transform: translateX(-100%);
  }
}
@keyframes animate4 {
  0% {
    transform: translateY(100%);
  }
  100% {
    transform: translateY(-100%);
  }
}

body.fixed-page {
  touch-action: none !important;
  overflow: hidden !important;
}

@keyframes modalDropTop {
  0% {
    /* transform: translate(-100%, -100%) scale(0); */
    opacity: 0;
    visibility: hidden;
  }
  100% {
    /* transform: translate(-50%, -50%) scale(1); */
    opacity: 1;
    visibility: visible;
  }
}

@keyframes shimmer {
  100% {
    transform: translateX(100%);
  }
}

.adsbygoogle {
  margin: 0 auto;
}

.adsbygoogle > * {
  margin: 0 auto !important;
  display: block !important;
}
