$width-mobile: 800px;

@mixin mobile {
  @media (max-width: #{$width-mobile}) {
    @content;
  }
}
.clout-custom-antd-radio .ant-radio-wrapper {
  .ant-radio {
    .ant-radio-inner {
      border: 2px solid #333 !important;
      width: 14px;
      height: 14px;
    }
    &.ant-radio-disabled {
      .ant-radio-inner {
        border: 2px solid #777 !important;
        background-color: rgba(0, 0, 0, 0.05) !important;
      }
    }
  }
  .ant-radio.ant-radio-checked {
    .ant-radio-inner {
      background: white !important;
      &::after {
        transform: scale(0.5) !important;
        background-color: #333 !important;
      }
    }
    &.ant-radio-disabled {
      .ant-radio-inner {
        &::after {
          background-color: #777 !important;
        }
      }
    }
  }
}

.clout-custom-antd-checkbox .ant-checkbox-wrapper {
  .ant-checkbox {
    .ant-checkbox-inner {
      background: #fff !important;
      border: 2px solid #333 !important;
      border-radius: 2px !important;
    }
    &.ant-checkbox-disabled {
      .ant-checkbox-inner {
        border: 2px solid #777 !important;
      }
    }
  }
  .ant-checkbox.ant-checkbox-checked {
    .ant-checkbox-inner {
      background-color: #333 !important;
      &::after {
        transform: rotate(45deg) scale(1) translate(-55%, -55%);
        border: 2px solid #fff !important;
        border-top: 0 !important;
        border-left: 0 !important;
      }
    }
    &.ant-checkbox-disabled {
      .ant-checkbox-inner {
        &::after {
          border: 2px solid #c921326c !important;
          border-top: 0 !important;
          border-left: 0 !important;
        }
      }
    }
  }
  .ant-checkbox.ant-checkbox-checked::after {
    border: none !important;
  }
}

.display-text-editer-content {
  font-size: 14px;
  h1 {
    font-size: 26px;
  }
  h2 {
    font-size: 20px;
  }
  h3 {
    font-size: 16px;
  }
  ol,
  ul {
    padding-left: 19.5px;
  }
  li {
    margin-left: 19.5px;
  }
  ol {
    list-style: decimal;
  }
  ul {
    list-style: disc;
  }
  a {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    color: #04afaf !important;
  }
}

div.custom-antd-notification {
  border-radius: 8px;
  padding: 16px 12px !important ;
  font-weight: 500 !important;
  .ant-notification-notice-close {
    width: 28px !important;
    height: 28px !important;
    inset-inline-end: 12px !important;
  }
  .ant-notification-notice-message {
    margin-inline-end: 28px !important;
    padding-right: 12px !important;
  }
}

.custom-antd-notification--error {
  // background-color: #ff0000;
  .ant-notification-notice-message {
    // color: #ff0000 !important;
  }
}

.custom-antd-notification--success {
  // background-color: #04afaf;
  .ant-notification-notice-message {
    // color: white !important;
  }
}

.scrollbar--custom::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
  background-color: #f5f5f5;
}

.scrollbar--custom::-webkit-scrollbar {
  width: 6px;
  background-color: #f5f5f5;
}

.scrollbar--custom::-webkit-scrollbar-thumb {
  background-color: #666;
}
.custom-scroll-bar::-webkit-scrollbar-thumb:hover {
  background: #333 !important;
}

.container-min-height {
  min-height: calc(100vh - var(--main-header-height-mobile) - var(--main-footer-height-mobile));
}

.form-multiple-answer-item {
  width: fit-content;
}
