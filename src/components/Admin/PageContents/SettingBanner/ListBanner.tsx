import styles from '@/components/common/BasicTable/index.module.scss';
import { useGetBannerQuery } from '@/redux/endpoints/banner';
import { Image, Table } from 'antd';
import React from 'react';
import type { ColumnsType } from 'antd/es/table';
import Link from 'next/link';
import CButtonShadow from '@/components/common/CButtonShadow';
import FileIcon from '@/components/common/icons/FileIcon';
import { useRouter } from 'next/router';
import { optionDeviceList, optionLocation, optionTypeBanner, TypeItemBannerResponse } from './type';

const ListBanner = () => {
  const { data: dataTable, isLoading, isFetching } = useGetBannerQuery({}, { refetchOnMountOrArgChange: true });
  const router = useRouter();
  const columns: ColumnsType<TypeItemBannerResponse> = [
    {
      title: 'ID',
      dataIndex: 'id',
      width: 100,
      render: (_, record, index) => (
        <span>
          {index + 1} (
          <Link
            className=" hover:underline underline-offset-2 cursor-pointer"
            href={`/admin/banner/detail/${record?.id ?? ''}`}
          >
            編集
          </Link>
          )
        </span>
      ),
    },
    {
      title: '位置',
      dataIndex: 'location',
      width: 100,
      render: (text: string) => <span>{optionLocation.find((i) => i.value === text)?.label}</span>,
    },
    {
      title: 'バナーの種類',
      dataIndex: 'type',
      width: 100,
      render: (text: string) => <span>{optionTypeBanner.find((i) => i.value === text)?.label}</span>,
    },
    {
      title: 'デバイス',
      dataIndex: 'device',
      width: 100,
      render: (text: string) => <span>{optionDeviceList.find((i) => i.value === text)?.label}</span>,
    },
    {
      title: '画像',
      dataIndex: 'images',
      width: 240,
      render: (image: TypeItemBannerResponse['images']) => (
        <div>
          <Image src={image?.[0]?.imageUrl ?? ''} style={{ height: 'auto', width: 'auto', maxHeight: '140px' }} />
          {image && image?.length > 1 ? <div>+ {Number(image.length - 1)}枚の画像</div> : ''}
        </div>
      ),
    },
  ];
  return (
    <div>
      <div className="flex justify-end mb-[16px]">
        <div className="md:w-[165px] w-[108px]  md:h-[56px] h-[46px]">
          <CButtonShadow
            classBgColor="bg-main-text"
            classRounded="rounded-[6px]"
            classShadowColor="bg-white"
            onClick={() => router.push('/admin/banner/create')}
            shadowSize="normal"
            textClass="md:text-[16px] text-[14px] text-white"
            title="バナーを作成する"
            withIcon={{ position: 'left', icon: <FileIcon color="#fff" /> }}
          />
        </div>
      </div>
      <div className={`${styles.customTable}`}>
        <Table
          columns={columns}
          dataSource={dataTable?.data}
          loading={isLoading || isFetching}
          pagination={false}
          rowKey="id"
          scroll={{ x: 700 }}
        />
      </div>
    </div>
  );
};

export default ListBanner;
