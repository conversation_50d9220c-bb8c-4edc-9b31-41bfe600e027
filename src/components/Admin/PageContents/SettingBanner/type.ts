export const DeviceType = {
  DESKTOP: 'desktop',
  MOBILE: 'mobile',
  BOTH: 'both',
};

export const LocationType = {
  TOP: 'top',
  FOOTER: 'footer',
  UNDER_CAMPAIGN_MISSION: 'under-campaign-mission',
};

export const optionDevice = [
  {
    label: 'PC',
    value: DeviceType.DESKTOP,
  },
  {
    label: 'モバイル',
    value: DeviceType.MOBILE,
  },
];

export const optionDeviceList = [
  {
    label: 'PC',
    value: DeviceType.DESKTOP,
  },
  {
    label: 'モバイル',
    value: DeviceType.MOBILE,
  },
  {
    label: 'PCとモバイル',
    value: DeviceType.BOTH,
  },
];

export const optionLocation = [
  {
    label: 'トップ',
    value: LocationType.TOP,
  },
  {
    label: 'フッター',
    value: LocationType.FOOTER,
  },
  {
    label: 'キャンペーンタスク下',
    value: LocationType.UNDER_CAMPAIGN_MISSION,
  },
];

export const optionTypeBanner = [
  {
    label: 'ローテーション',
    value: 'Carousel',
  },
  // {
  //   label: '回転',
  //   value: 'Rotation',
  // },
  {
    label: '固定',
    value: 'Fixed',
  },
];

export interface TypeItemBannerResponse {
  device?: string | null | undefined;
  id?: number | null | undefined;
  location?: string | null | undefined;
  type?: string | null | undefined;
  images?: {
    alt_image?: string | null | undefined;
    bannerId?: number | null | undefined;
    id?: number | null | undefined;
    imageUrl?: string | null | undefined;
    createdAt?: string | null | undefined;
    uploadAt?: string | null | undefined;
    updatedAt?: string | null | undefined;
    url?: string | null | undefined;
    dynamic_url?: string | null | undefined;
  }[];
}

export interface MenuItemType {
  label: string | null | undefined;
  icon: string | null | undefined;
  key: string | null | undefined;
  type?: string | null | undefined;
  children?: MenuItemType[] | undefined | null;
}
