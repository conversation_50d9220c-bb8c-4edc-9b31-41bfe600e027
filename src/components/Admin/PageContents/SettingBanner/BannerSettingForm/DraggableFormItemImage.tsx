import InputLabel from '@/components/common/BasicInput/InputLabel';
import HolderIcon from '@/components/common/icons/HolderIcon';
import XIcon from '@/components/common/icons/XIcon';
import UploadButton from '@/components/common/UploadButton';
import { useSortable } from '@dnd-kit/sortable';
// eslint-disable-next-line import/no-extraneous-dependencies
import { Checkbox, Form } from 'antd';
import React from 'react';

interface FormListItemProps {
  restField: { fieldKey?: number | undefined };
  name: number;
  remove: () => void;
  aspectImage: number | undefined;
  disable: boolean;
}

const DraggableFormItemImage = ({ restField, name, remove, aspectImage, disable }: FormListItemProps) => {
  const { attributes, listeners, setNodeRef, transform, transition } = useSortable({ id: name });

  const style = {
    transform: transform ? `translate(${transform.x}px, ${transform.y}px)` : 'none',
    transition,
  };

  return (
    <div
      className=" border p-[12px] border-[#333333] rounded-[8px] relative z-[2] bg-white"
      ref={setNodeRef}
      style={style}
    >
      <div className="flex justify-end">
        <button
          aria-label="close"
          className="cursor-pointer !border-none w-fit !bg-none"
          onClick={() => {
            remove();
          }}
          type="button"
        >
          <XIcon height={32} width={32} />
        </button>
      </div>

      <div className="flex justify-between space-x-[24px]">
        <div className="flex-shrink-0">
          <div className="flex space-x-[8px] items-center mb-[10px]">
            <div className="text-[14px] font-bold ">画像</div>
            <div className="w-[35px] bg-[#04AFAF] h-[20px] text-center text-[11px] font-medium leading-[1.7] text-white rounded-[2px]">
              必須
            </div>
          </div>
          <Form.Item
            {...restField}
            className="w-fit min-w-[170px]"
            name={[name, 'image']}
            rules={[{ required: true, message: '画像を追加してください' }]}
          >
            <UploadButton
              centeredModalPreview
              className="[&_.ant-upload-list-item-container]:!min-h-[160px] [&_.ant-upload-list-item-container]:!min-w-[280px]"
              disabled={disable}
              isFullSize
              props={{ aspect: aspectImage }}
            />
          </Form.Item>
        </div>
        <div className="flex-1">
          <InputLabel
            {...restField}
            disabled={disable}
            label="URL"
            name={[name, 'url']}
            placeholder="URLを入力してください"
            rules={[{ type: 'url', message: '' }]}
          />
        </div>
      </div>
      <div
        {...attributes}
        {...listeners}
        className="absolute left-[-10.5px] top-1/2 transform -translate-y-1/2 z-[3] bg-white cursor-move"
      >
        <HolderIcon />
      </div>
    </div>
  );
};

export default DraggableFormItemImage;
