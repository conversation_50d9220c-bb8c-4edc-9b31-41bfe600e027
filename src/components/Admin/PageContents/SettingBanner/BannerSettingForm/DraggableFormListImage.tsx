import BasicButton from '@/components/common/BasicButton';
import { Form, FormListFieldData } from 'antd';
import {
  closestCenter,
  DndContext,
  DragEndEvent,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
} from '@dnd-kit/core';
import { SortableContext, sortableKeyboardCoordinates, verticalListSortingStrategy } from '@dnd-kit/sortable';
import { restrictToParentElement, restrictToVerticalAxis } from '@dnd-kit/modifiers';
import DraggableFormItemImage from './DraggableFormItemImage';

interface Props {
  aspectImage: number | undefined;
  hasDevice: boolean;
  locationBanner: string | undefined;
}

const DraggableFormListImage = ({ aspectImage, hasDevice, locationBanner }: Props) => {
  // Hàm xử lý khi kéo thả xong
  const handleDragEnd = (
    event: DragEndEvent,
    fields: FormListFieldData[],
    move: (from: number, to: number) => void
  ) => {
    const { active, over } = event;
    if (!over || active.id === over.id) return;

    const oldIndex = fields.findIndex((item) => item.name === active.id);
    const newIndex = fields.findIndex((item) => item.name === over.id);

    if (oldIndex !== newIndex) {
      move(oldIndex, newIndex);
    }
  };

  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  return (
    <div>
      <div className="text-[14px] font-bold mb-[10px]">画像リスト</div>
      <Form.List initialValue={[{ url: '' }]} name="images">
        {(fields, { add, remove, move }) => (
          <>
            <DndContext
              collisionDetection={closestCenter}
              modifiers={[restrictToVerticalAxis, restrictToParentElement]}
              onDragEnd={(event) => handleDragEnd(event, fields, move)}
              sensors={sensors}
            >
              <SortableContext items={fields.map((field) => field.name)} strategy={verticalListSortingStrategy}>
                <div className="space-y-[8px]">
                  {fields.map(({ key, name, ...restField }) => (
                    <DraggableFormItemImage
                      aspectImage={aspectImage}
                      disable={!locationBanner || !hasDevice}
                      key={key}
                      name={name}
                      remove={() => remove(name)}
                      restField={restField}
                    />
                  ))}
                </div>
              </SortableContext>
            </DndContext>
            {(fields.length <= 4 && locationBanner === 'top') || fields.length === 0 ? (
              <div className="flex justify-end mt-[12px]">
                <BasicButton
                  className="w-[150px] "
                  onClick={() => {
                    add();
                  }}
                  type="primary"
                >
                  + 画像を追加する
                </BasicButton>
              </div>
            ) : (
              ''
            )}
          </>
        )}
      </Form.List>
    </div>
  );
};

export default DraggableFormListImage;
