/* eslint-disable max-lines-per-function */
import SelectLabel from '@/components/common/BasicSelect/SelectLabel';
import CButtonShadow from '@/components/common/CButtonShadow';
import { useCreateBannerMutation, useUpdateBannerMutation } from '@/redux/endpoints/banner';
import toastMessage from '@/utils/func/toastMessage';
import { SerializedError } from '@reduxjs/toolkit';
import { FetchBaseQueryError } from '@reduxjs/toolkit/query';
import { Form, Spin } from 'antd';
import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';
import {
  DeviceType,
  LocationType,
  optionDevice,
  optionLocation,
  optionTypeBanner,
  TypeItemBannerResponse,
} from '../type';
import DraggableFormListImage from './DraggableFormListImage';

interface TypeBanner {
  location?: string | null | undefined;
  type?: string | null | undefined;
  device?: string | null | undefined;
  images?: {
    id?: string | number | undefined | null;
    url?: string | null | undefined;
    alt?: string | null | undefined;
    image?: File | string | null | undefined;
    dynamic_url?: boolean | null | undefined;
  }[];
}

interface Props {
  type?: 'edit' | 'create';
  dataInitial?: TypeItemBannerResponse | undefined;
  id?: string | string[] | undefined;
}

const addCacheBuster = (url: string) => {
  const cacheBuster = `cb=${new Date().getTime()}`;
  return url.includes('?') ? `${url}&${cacheBuster}` : `${url}?${cacheBuster}`;
};

const getPosition = (location: string, device: string) => {
  if (location === LocationType.TOP && device === DeviceType.DESKTOP) {
    return '1';
  }
  if (location === LocationType.TOP && device === DeviceType.MOBILE) {
    return '2';
  }
  if (location === LocationType.UNDER_CAMPAIGN_MISSION) {
    return '3';
  }
  if (location === LocationType.FOOTER && device === DeviceType.DESKTOP) {
    return '4';
  }
  if (location === LocationType.FOOTER && device === DeviceType.MOBILE) {
    return '5';
  }
  return '6';
};

const BannerSettingForm = ({ type, dataInitial, id }: Props) => {
  const [form] = Form.useForm();
  const locationBanner = Form.useWatch('location', form);
  const deviceBanner = Form.useWatch('device', form);
  const [createTrigger, { isLoading: loadingCreate }] = useCreateBannerMutation();
  const [updateTrigger, { isLoading: loadingUpdate }] = useUpdateBannerMutation();
  const [aspectImage, setAspectImage] = useState<number | undefined>();

  const router = useRouter();

  useEffect(() => {
    if (type === 'edit' && !!dataInitial && form) {
      form.setFieldsValue({
        location: dataInitial?.location,
        device: dataInitial?.device,
        type: dataInitial?.type,
        images:
          dataInitial?.images && dataInitial?.images.length > 0
            ? dataInitial?.images?.map((i) => ({
                alt: i?.alt_image,
                url: i?.url,
                id: i?.id,
                dynamic_url: Boolean(i?.dynamic_url),
                image: {
                  id: i?.id,
                  uploadAt: i?.updatedAt,
                  createdAt: i?.createdAt,
                  updatedAt: i?.updatedAt,
                  imageUrl: addCacheBuster(i?.imageUrl ?? ''),
                },
              }))
            : [{ url: '' }],
      });
    }
  }, [type, dataInitial, form]);

  useEffect(() => {
    if (!!deviceBanner && !!locationBanner) {
      if (deviceBanner === DeviceType.DESKTOP && locationBanner === LocationType.TOP) {
        setAspectImage(970 / 250);
        return;
      }
      if (deviceBanner === DeviceType.DESKTOP && locationBanner === LocationType.FOOTER) {
        setAspectImage(970 / 90);
        return;
      }
      setAspectImage(336 / 250);
    } else {
      if (locationBanner === LocationType.UNDER_CAMPAIGN_MISSION) {
        setAspectImage(336 / 250);
        return;
      }
      setAspectImage(undefined);
    }
  }, [deviceBanner, locationBanner]);

  const handleSubmitBanner = async (value: TypeBanner) => {
    const position = getPosition(value?.location ?? '', value?.device ?? '');
    if (type === 'create') {
      const dataSent = new FormData();
      const newImage = value?.images?.filter((i) => !!i?.image);
      dataSent.append('type', value?.type ?? '');
      dataSent.append('device', value?.location === 'under-campaign-mission' ? 'both' : value?.device ?? '');
      dataSent.append('location', value?.location ?? '');
      if (position) {
        dataSent.append('position', position);
      }
      if (newImage && newImage?.length > 0) {
        newImage.forEach((item, index) => {
          dataSent.append(`images[${index}].image`, item?.image ?? '');
          dataSent.append(`images[${index}].alt`, item?.alt ?? '');
          dataSent.append(`images[${index}].url`, item?.url ?? '');
          dataSent.append(`images[${index}].position`, Number(index + 1).toString());
          dataSent.append(`images[${index}].dynamic_url`, Boolean(item?.dynamic_url).toString());
        });
      }
      const dataSubmit = (await createTrigger(dataSent)) as {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        data: any;
        error: FetchBaseQueryError | SerializedError;
      };
      if (dataSubmit && dataSubmit?.data?.status === 'success') {
        toastMessage('バナーの追加が成功しました', 'success');
        router.push('/admin/banner');
      } else {
        toastMessage('バナーの追加が失敗しました', 'error');
      }
    } else if (type === 'edit') {
      const dataSent = new FormData();
      const dataImageUpdate = value?.images?.filter((i) => (i?.id && i?.image) || !i?.id);
      const dataDelete = dataInitial?.images?.filter(
        (i) => !!i?.id && !value?.images?.some((item) => item?.id === i?.id)
      );
      dataSent.append('type', value?.type ?? '');
      dataSent.append('device', value?.location === 'under-campaign-mission' ? 'both' : value?.device ?? '');
      dataSent.append('location', value?.location ?? '');
      if (position) {
        dataSent.append('position', position);
      }
      if (dataImageUpdate && dataImageUpdate?.length > 0) {
        dataImageUpdate.forEach((item, index) => {
          if (item.image instanceof File) {
            dataSent.append(`images[${index}].image`, item?.image ?? '');
          }
          dataSent.append(`images[${index}].alt`, item?.alt ?? '');
          dataSent.append(`images[${index}].position`, Number(index + 1).toString());
          dataSent.append(`images[${index}].url`, item?.url ?? '');
          dataSent.append(`images[${index}].dynamic_url`, Boolean(item?.dynamic_url).toString());
          if (item?.id) {
            dataSent.append(`images[${index}].id`, item.id?.toString());
          }
        });
      }
      if (dataDelete && dataDelete?.length > 0) {
        dataDelete?.forEach?.((item, index) => {
          dataSent.append(`listIdImageDelete[${index}]`, String(item.id));
        });
      }
      const dataSubmit = (await updateTrigger({ dataSent, id })) as {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        data: any;
        error: FetchBaseQueryError | SerializedError;
      };
      if (dataSubmit && dataSubmit?.data?.status === 'success') {
        toastMessage('バナーの修正が成功しました', 'success');
        router.push('/admin/banner');
      } else {
        toastMessage('バナーの修正が失敗しました', 'error');
      }
    }
  };

  useEffect(() => {
    if (locationBanner) {
      if (locationBanner === 'top') {
        form.setFieldValue('type', 'Carousel');
      } else {
        form.setFieldValue('type', 'Fixed');
        const dataImageCurrent = form.getFieldValue('images');
        if (dataImageCurrent && dataImageCurrent?.length > 1) {
          form.setFieldValue('images', [dataImageCurrent[0]]);
        }
      }
    }
  }, [locationBanner]);

  // reset image if reselect device type or banner location
  useEffect(() => {
    if (type === 'create') {
      if (!!locationBanner || !!deviceBanner) {
        const dataImageCurrent = form.getFieldValue('images');
        if (dataImageCurrent && dataImageCurrent?.length > 0) {
          form.resetFields(['images']);
          form.setFieldValue('images', [{ url: '' }]);
        }
      }
    }
  }, [type, locationBanner, deviceBanner]);

  return (
    <Spin spinning={loadingCreate || loadingUpdate}>
      <div className="flex justify-end mb-[16px]">
        <div className="w-full md:w-[217px]   md:h-[56px] h-[46px] ">
          <CButtonShadow
            classBgColor="bg-main-text"
            classRounded="rounded-[6px]"
            classShadowColor="bg-white"
            onClick={() => {
              router.push('/admin/banner');
            }}
            shadowSize="normal"
            title="バナーリストに戻る"
            withIcon={{
              position: 'left',
              icon: (
                <svg fill="none" height="18" viewBox="0 0 18 18" width="18" xmlns="http://www.w3.org/2000/svg">
                  <path
                    d="M17.875 8.5625C17.9375 8.6875 17.9688 8.84375 17.9688 9.03125C17.9688 9.1875 17.9375 9.34375 17.875 9.46875C16.1875 12.7812 12.8125 15 9 15C5.15625 15 1.78125 12.7812 0.09375 9.46875C0.03125 9.34375 0 9.1875 0 9C0 8.84375 0.03125 8.6875 0.09375 8.5625C1.78125 5.25 5.15625 3 9 3C12.8125 3 16.1875 5.25 17.875 8.5625ZM9 13.5V13.5312C11.4688 13.5312 13.5 11.5 13.5 9.03125V9C13.5 6.53125 11.4688 4.5 9 4.5C6.5 4.5 4.5 6.53125 4.5 9C4.5 11.5 6.5 13.5 9 13.5ZM9 6V6.03125C10.6562 6.03125 12 7.34375 12 9C12 10.6562 10.6562 12 9 12C7.34375 12 6 10.6562 6 9C6 8.75 6.03125 8.46875 6.09375 8.21875C6.34375 8.40625 6.65625 8.5 7 8.5C7.8125 8.5 8.46875 7.84375 8.46875 7.03125C8.46875 6.6875 8.375 6.375 8.1875 6.125C8.4375 6.0625 8.71875 6.03125 9 6Z"
                    fill="white"
                  />
                </svg>
              ),
            }}
          />
        </div>
      </div>
      <div className="p-[12px] bg-[#FFF] rounded-[8px]">
        <Form
          form={form}
          onFinish={handleSubmitBanner}
          scrollToFirstError={{ behavior: 'smooth', inline: 'center', block: 'center' }}
        >
          <SelectLabel
            disabled={type === 'edit'}
            label="バナーの位置"
            name="location"
            options={optionLocation}
            placeholder="バナーの位置を選択してください"
            required
            rules={[{ required: true, message: '' }]}
          />
          {locationBanner !== 'under-campaign-mission' ? (
            <SelectLabel
              disabled={type === 'edit'}
              label="デバイスタイプ"
              name="device"
              options={optionDevice}
              placeholder="デバイスタイプを選択してください"
              required
              rules={[{ required: true, message: '' }]}
            />
          ) : (
            ''
          )}
          <SelectLabel
            disabled
            label="バナーの種類"
            name="type"
            options={optionTypeBanner}
            placeholder=""
            required
            rules={[{ required: true, message: '' }]}
          />
          <DraggableFormListImage
            aspectImage={aspectImage}
            hasDevice={!!deviceBanner || locationBanner === 'under-campaign-mission'}
            locationBanner={locationBanner}
          />
          <div className="w-[206px] h-[50px] mx-auto mt-[20px]">
            <CButtonShadow onClick={() => form.submit()} title="保存" type="button" />
          </div>
        </Form>
      </div>
    </Spin>
  );
};

BannerSettingForm.defaultProps = {
  type: 'create',
  dataInitial: undefined,
  id: undefined,
};

export default BannerSettingForm;
