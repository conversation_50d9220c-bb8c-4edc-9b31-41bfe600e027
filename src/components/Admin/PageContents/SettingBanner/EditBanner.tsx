import { useGetDetailBannerQuery } from '@/redux/endpoints/banner';
import { Spin } from 'antd';
import { useRouter } from 'next/router';
import React from 'react';
import BannerSettingForm from './BannerSettingForm';

function EditBanner() {
  const router = useRouter();
  const { id } = router.query;
  const { data, isFetching } = useGetDetailBannerQuery({ id }, { refetchOnMountOrArgChange: true, skip: !id });
  return (
    <Spin spinning={isFetching}>
      <BannerSettingForm dataInitial={data?.data} id={id} type="edit" />
    </Spin>
  );
}
export default EditBanner;
