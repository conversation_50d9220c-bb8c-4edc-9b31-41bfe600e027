/* eslint-disable max-lines-per-function */
/* eslint-disable react/jsx-no-useless-fragment */
import {
  DataMaintenanceItem,
  useDeleteMaintenanceMutation,
  useGetMaintenanceQuery,
} from '@/redux/endpoints/maintenance';
import { <PERSON><PERSON>, Card, Popconfirm, Spin } from 'antd';
import moment from 'moment';
import React, { useEffect, useState } from 'react';
import toastMessage from '@/utils/func/toastMessage';
import EditMaintenance from './EditMaintenance';

const MaintenanceTimeSetting: React.FC = () => {
  const [deleteMaintenance] = useDeleteMaintenanceMutation();
  const { data: campaignDetailTasks, isFetching: isFetchingCampaignTasks, isError } = useGetMaintenanceQuery({});
  const [maintenance, setMaintenance] = useState<DataMaintenanceItem | undefined>();
  const [type, setType] = useState('view');
  const [open, setOpen] = useState(false);

  useEffect(() => {
    if (campaignDetailTasks?.data?.data) {
      const activeMaintenance = campaignDetailTasks?.data?.data?.find((item) => item);
      setMaintenance(activeMaintenance);
    }
    setType('view');
  }, [campaignDetailTasks]);

  useEffect(() => {
    if (isError) {
      toastMessage('データ取得エラー', 'error');
    }
  }, []);

  const confirm = async () => {
    if (maintenance) {
      try {
        const dataDelete = await deleteMaintenance({ id: maintenance.id }).unwrap();
        if (dataDelete) {
          toastMessage('削除に成功しました。', 'success');
          setMaintenance(undefined);
          setOpen(false);
        } else {
          toastMessage('削除エラー', 'error');
        }
      } catch (error) {
        toastMessage('削除エラー', 'error');
      }
    }
  };

  const cancel = () => {
    setOpen(false);
  };

  const handleOpenChange = (newOpen: boolean) => {
    if (!newOpen) {
      setOpen(newOpen);
      return;
    }
    setOpen(newOpen);
  };

  return (
    <div>
      {isFetchingCampaignTasks ? (
        <Spin />
      ) : (
        <>
          {type === 'view' && (
            <>
              {maintenance ? (
                <Card className="border-1 border-[#2D3648]" style={{}} title="予約済みのメンテナンス日時">
                  <p className="mr-2">{maintenance.description}</p>
                  <p>
                    {moment(maintenance.startAt).format('YYYY/MM/DD HH:mm')} ~
                    {moment(maintenance.endAt).format('YYYY/MM/DD HH:mm')}
                  </p>
                  <hr className="h-[2px] border-none bg-[#2D3648] my-[40px]" />
                  <div className="flex space-x-6">
                    <Popconfirm
                      cancelText="いいえ"
                      description="このスケジュールを削除してもよろしいですか?"
                      okText="はい"
                      onCancel={cancel}
                      onConfirm={confirm}
                      onOpenChange={handleOpenChange}
                      open={open}
                      title="スケジュールを削除する"
                    >
                      <Button className="border-[#2D3648] text-[#2D3648] h-[56px] px-5 font-bold" type="default">
                        予約削除
                      </Button>
                    </Popconfirm>

                    <Button
                      className="bg-[#2D3648] text-white h-[56px] px-5 font-bold"
                      onClick={() => setType('edit')}
                      type="primary"
                    >
                      編集
                    </Button>
                  </div>
                </Card>
              ) : (
                <>
                  <Card className="border-1 border-[#2D3648]" style={{}} title="予約済みのメンテナンス日時">
                    <p className="mr-2">メンテナンススケジュールはありません</p>
                    <hr className="h-[2px] border-none bg-[#2D3648] my-[40px]" />
                    <Button
                      className="border-[#2D3648] text-[#2D3648] h-[56px] px-5 font-bold"
                      onClick={() => setType('edit')}
                      type="default"
                    >
                      新規作成
                    </Button>
                  </Card>
                </>
              )}
            </>
          )}
        </>
      )}
      {type === 'edit' && <EditMaintenance maintenance={maintenance} setType={setType} />}
    </div>
  );
};

export default MaintenanceTimeSetting;
