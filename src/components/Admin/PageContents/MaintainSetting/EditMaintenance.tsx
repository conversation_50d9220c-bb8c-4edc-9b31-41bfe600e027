/* eslint-disable max-lines-per-function */
/* eslint-disable react/jsx-no-useless-fragment */
/* eslint-disable */

import BasicDatePicker from '@/components/common/BasicDatePicker';
import BasicTextArea from '@/components/common/BasicTextArea';
import {
  DataMaintenanceItem,
  useCreateMaintenanceMutation,
  useUpdateMaintenanceMutation,
} from '@/redux/endpoints/maintenance';
import toastMessage from '@/utils/func/toastMessage';
import { range } from '@/utils/range';
import { Button, Card, Form, Typography } from 'antd';
import dayjs, { Dayjs } from 'dayjs';
import moment from 'moment';
import React, { useEffect } from 'react';

const EditMaintenance = ({
  maintenance,
  setType,
}: {
  maintenance: DataMaintenanceItem | undefined;
  setType: (value: string) => void;
}) => {
  const [createMaintenance] = useCreateMaintenanceMutation();
  const [updateMaintenance] = useUpdateMaintenanceMutation();
  const [form] = Form.useForm();
  const startDateWatch = Form.useWatch('startDate', form);

  useEffect(() => {
    if (maintenance) {
      form.setFieldsValue({
        startDate: dayjs(maintenance.startAt),
        endDate: dayjs(maintenance.endAt),
        // reason: maintenance?.description,
      });
    }
  }, [maintenance]);

  const handleCreateMaintenance = async (value: { startDate: Dayjs; endDate: Dayjs; reason?: string }) => {
    try {
      const dataCreate = await createMaintenance({
        startAt: dayjs(value.startDate).toISOString(),
        endAt: dayjs(value.endDate).toISOString(),
        // description: value?.reason,
      }).unwrap();
      if (dataCreate?.id) {
        toastMessage('メンテナンスを予約しました。', 'success');
      } else {
        toastMessage('エラーの作成', 'error');
      }
    } catch (error) {
      toastMessage('エラーの作成', 'error');
    }
  };

  const handleUpdateMaintenance = async (value: { startDate: Dayjs; endDate: Dayjs; reason?: string }) => {
    try {
      if (maintenance) {
        const dataUpdate = await updateMaintenance({
          id: maintenance.id,
          startAt: dayjs(value.startDate).toISOString(),
          endAt: dayjs(value.endDate).toISOString(),
          description: value?.reason,
        }).unwrap();
        if (dataUpdate) {
          toastMessage('更新成功。', 'success');
        } else {
          toastMessage('更新エラー', 'error');
        }
      }
    } catch (error) {
      toastMessage('更新エラー', 'error');
    }
  };

  const validateStartDate = (_, value) => {
    if (!value || !form.getFieldValue('endDate') || dayjs(value).isBefore(form.getFieldValue('endDate'))) {
      return Promise.resolve();
    }
    return Promise.reject(new Error('開始日は終了日より前にする必要があります。'));
  };

  const validateEndDate = (_, value) => {
    if (!value || !form.getFieldValue('startDate') || dayjs(value).isAfter(form.getFieldValue('startDate'))) {
      return Promise.resolve();
    }
    return Promise.reject(new Error('終了日は開始日より後である必要があります。'));
  };

  return (
    <Card className="border-1 border-[#2D3648]" style={{}}>
      <Form
        form={form}
        layout="vertical"
        onFinish={(value) => {
          if (maintenance) {
            handleUpdateMaintenance(value);
          } else {
            handleCreateMaintenance(value);
          }
        }}
      >
        <div className="">
          <Typography.Title level={5}>
            開始日時{' '}
            <span className="bg-[#04AFAF] px-[7px] py-[3px] ml-2 text-white font-medium text-[11px]">必須</span>
          </Typography.Title>
          <Form.Item
            name="startDate"
            rules={[{ required: true, message: 'お願いします' }, { validator: validateStartDate }]}
          >
            <BasicDatePicker
              disabledDate={(current) => dayjs(current) < dayjs().add(-1, 'day')}
              disabledTime={(current) => {
                if (dayjs(current).format('DD') === dayjs().format('DD')) {
                  return {
                    disabledHours: () => range(0, Number(dayjs().format('HH'))),
                    disabledMinutes: () => {
                      if (dayjs(current).format('HH') === dayjs().format('HH')) {
                        return range(0, Number(dayjs().format('mm')));
                      }
                      return range(0, 0);
                    },
                  };
                }
                return {
                  disabledHours: () => range(0, Number(dayjs(current).startOf('day').format('HH'))),
                  disabledMinutes: () => range(0, Number(dayjs(current).startOf('day').format('mm'))),
                };
              }}
              format="YYYY-MM-DD HH:mm"
              placeholder="開始日時を選択してください"
              showTime
            />
          </Form.Item>
        </div>
        <div className="">
          <Typography.Title level={5}>
            終了日時
            <span className="bg-[#04AFAF] px-[7px] py-[3px] ml-2 text-white font-medium text-[11px]">必須</span>
          </Typography.Title>
          <Form.Item
            name="endDate"
            rules={[{ required: true, message: 'お願いします' }, { validator: validateEndDate }]}
          >
            <BasicDatePicker
              disabledDate={(current) =>
                moment(current.format('YYYY-MM-DD')) < moment(startDateWatch?.format('YYYY-MM-DD'))
              }
              disabledTime={(current) => {
                if (dayjs(current).format('DD') === dayjs(startDateWatch).format('DD')) {
                  return {
                    disabledHours: () => range(0, Number(dayjs(startDateWatch).format('HH'))),
                    disabledMinutes: () => {
                      if (dayjs(current).format('HH') === dayjs(startDateWatch).format('HH')) {
                        return range(0, Number(dayjs(startDateWatch).add(15, 'minutes').format('mm')));
                      }
                      return range(0, 0);
                    },
                  };
                }
                return {
                  disabledHours: () => range(0, Number(dayjs(current).startOf('day').format('HH'))),
                  disabledMinutes: () => range(0, Number(dayjs(current).startOf('day').format('mm'))),
                };
              }}
              format="YYYY-MM-DD HH:mm"
              placeholder="終了日時を選択してください"
              showNow
              showTime
            />
          </Form.Item>
        </div>
        {/* <div className="reason">
          <Typography.Title level={5}>メンテナンス理由</Typography.Title>
          <Form.Item name="reason">
            <BasicTextArea placeholder="理由を入力してください" />
          </Form.Item>
        </div> */}
        <hr className="h-[2px] border-none bg-[#2D3648] my-[40px]" />
        <div className="flex space-x-6">
          <Button
            className="border-[#2D3648] text-[#2D3648] h-[56px] px-5 font-bold"
            onClick={() => setType('view')}
            type="default"
          >
            キャンセル
          </Button>
          <Form.Item>
            <Button
              className="bg-[#2D3648] text-white h-[56px] px-5 font-bold"
              htmlType="submit"
              onClick={() => setType('edit')}
              type="primary"
            >
              上記内容で予約
            </Button>
          </Form.Item>
        </div>
      </Form>
    </Card>
  );
};

export default EditMaintenance;
