import useWindowDimensions from '@/hooks/useWindowDimensions';
import { logout } from '@/redux/slices/auth.slice';
import { RootState } from '@/redux/store';
import { Image, Layout, Menu, theme } from 'antd';
import type { MenuProps } from 'antd';
import { useRouter } from 'next/router';
import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import Header from './Header';
import styles from './index.module.scss';

const { Content, Sider } = Layout;

interface TypeItemMenu {
  label: string;
  icon: string;
  key: string;
  specialPage?: boolean | null | undefined;
}

const findKeyActive = (keys: TypeItemMenu[], path) => {
  const sortedKeys = keys.sort((a, b) => b.key.length - a.key.length);
  const foundKey = sortedKeys.find((item) => path.startsWith(item?.key))?.key;
  return foundKey ?? '';
};

const findKeySpecial = (keys: TypeItemMenu[], path) => {
  const sortedKeys = keys.sort((a, b) => b.key.length - a.key.length);
  return sortedKeys.find((item) => path.startsWith(item?.key))?.specialPage ?? false;
};

const LayoutAdmin = ({ children }: { children: React.ReactNode }) => {
  const {
    token: { colorBgContainer, borderRadiusLG },
  } = theme.useToken();
  const router = useRouter();
  const { width } = useWindowDimensions();
  const [collapsed, setCollapsed] = useState(false);
  const { user } = useSelector((state: RootState) => state.auth);
  const dispatch = useDispatch();
  const [isSpecial, setIsSpecial] = useState<boolean>(false);

  const MenuSlider: TypeItemMenu[] = [
    {
      label: 'メンテナンス日時設定',
      icon: '/assets/icons/admin/Shape.svg',
      key: '/admin',
    },
    {
      label: 'バナーリスト',
      icon: '/assets/icons/admin/Shape.svg',
      key: '/admin/banner',
      specialPage: true,
    },
  ];

  const items2: MenuProps['items'] = MenuSlider.map((menuItem) => ({
    key: menuItem.key,
    label: menuItem.label,
    icon: <Image alt="logo" className="cursor-pointer" height={12} preview={false} src={menuItem.icon} />,
  }));

  useEffect(() => {
    if (!user?.isAdmin) {
      dispatch(logout());
      router.push('/admin/login');
    }
  }, [user]);

  useEffect(() => {
    if (router?.pathname && MenuSlider && MenuSlider?.length > 0) {
      const itemSpecial = findKeySpecial(MenuSlider, router?.pathname);
      setIsSpecial(itemSpecial);
    }
  }, [router?.pathname, MenuSlider]);

  return (
    <Layout className={`${styles.customAdminLayout} min-h-[100vh]`}>
      <Header />
      <Layout className="flex-1">
        <Sider
          className="border-[#2D3648] border-r-2"
          collapsed={collapsed}
          collapsible
          onCollapse={(value) => setCollapsed(value)}
          style={{ background: colorBgContainer }}
          width={width > 768 ? 258 : 200}
        >
          <Menu
            defaultOpenKeys={[findKeyActive(MenuSlider, router?.pathname)]}
            defaultSelectedKeys={[findKeyActive(MenuSlider, router?.pathname)]}
            items={items2}
            mode="inline"
            onClick={(itemSelected) => {
              const selectedKey = itemSelected.key;
              if (router.pathname !== selectedKey) {
                router.push(selectedKey);
              }
            }}
            selectedKeys={[findKeyActive(MenuSlider, router?.pathname)]}
            style={{ height: '100%', borderRight: 0 }}
          />
        </Sider>
        <Layout style={{ padding: '0' }}>
          {MenuSlider.find((item) => item.key === router?.pathname) && !isSpecial ? (
            <div className="h-[60px] md:h-[106px] px-4 sm:px-12 bg-white border-[#2D3648] border-b-2 flex items-center">
              <h2 className="font-bold text-[14px] sm:text-[18px] md:text-[32px]">
                {MenuSlider.find((item) => router?.pathname?.startsWith(item.key))?.label}
              </h2>
            </div>
          ) : (
            ''
          )}
          <Content
            style={{
              // eslint-disable-next-line no-nested-ternary
              padding: width > 768 ? (isSpecial ? 16 : 48) : 8,
              margin: 0,
              minHeight: 300,
              minWidth: 320,
              background: !isSpecial ? colorBgContainer : '#F6F6F6',
              borderRadius: borderRadiusLG,
            }}
          >
            {children}
          </Content>
        </Layout>
      </Layout>
    </Layout>
  );
};

export default LayoutAdmin;
