import useWindowDimensions from '@/hooks/useWindowDimensions';
import { logout } from '@/redux/slices/auth.slice';
import { RootState } from '@/redux/store';
import { Button, Image } from 'antd';
import { useRouter } from 'next/router';
import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';

const LayoutAdmin: React.FC = () => {
  const { user } = useSelector((state: RootState) => state.auth);
  const router = useRouter();
  const dispatch = useDispatch();
  const { width } = useWindowDimensions();

  useEffect(() => {
    if (user && !user?.isAdmin) {
      dispatch(logout());
      router.push('/admin/login');
    }
  }, [user]);

  return (
    <div className="bg-white border-[#2D3648] border-b-2 h-[60px] flex items-center justify-between px-[40px]">
      <Image
        alt="logo"
        className="cursor-pointer"
        height={30}
        onClick={() => router.push('/')}
        preview={false}
        src="/assets/images/logo 1.png"
      />

      <div className="flex space-x-[8px] text-[14px] text-[#333] font-medium items-center ">
        <div className="flex space-x-[16px] h-[21px]">
          {width > 800 && <span className="">サイト管理者</span>}
          {width > 500 && <span>{user?.email?.email}</span>}
        </div>
        <div>
          <Button
            className="p-0"
            onClick={() => {
              dispatch(logout());
              router.push('/admin/login');
            }}
            type="link"
          >
            ログアウト
          </Button>
        </div>
      </div>
    </div>
  );
};

export default LayoutAdmin;
