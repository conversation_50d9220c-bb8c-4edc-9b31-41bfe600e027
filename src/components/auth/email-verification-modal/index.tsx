import CModalWapper from '@/components/common/CModalWapper';

import { Spin } from 'antd';
import SmsVerificationForm from '../sms-verification-form';

export default function EmailVerificationModal({
  isOpen,
  isSubmitError,
  isSubmitting,
  emailVerification,
  onCancel,
  onSubmitCode,
  onResendCode,
}: {
  isOpen: boolean;
  isSubmitError: boolean;
  isSubmitting: boolean;
  emailVerification: string;
  onCancel: () => void;
  onSubmitCode: (code: string) => void;
  onResendCode: () => void;
}) {
  return (
    <CModalWapper isOpen={isOpen} onCancel={onCancel}>
      <Spin spinning={isSubmitting}>
        <div className="font-notoSans ml-[-16px] mr-[-16px]">
          <h3 className="text-[20px] font-bold tracking-[0.6px] leading-[30px] text-center">
            認証コードを入力してください
          </h3>
          <div className="h-[24px]" />
          <SmsVerificationForm isSubmitError={isSubmitError} onSubmitCode={onSubmitCode} />
          <div className="h-[24px]" />
          <p className="text-[13px] font-medium text-center">
            確認コードを含むメールを{emailVerification ?? ''}に送信しました。
          </p>
          <div className="h-[4px]" />
          <p className="text-[13px] text-center">続けるにはコードを入力してください。</p>
          <div className="h-[24px]" />

          <p aria-hidden className="text-[13px] text-center  text-[#04afaf] cursor-pointer" onClick={onResendCode}>
            コードを再送信する
          </p>
        </div>
      </Spin>
    </CModalWapper>
  );
}
