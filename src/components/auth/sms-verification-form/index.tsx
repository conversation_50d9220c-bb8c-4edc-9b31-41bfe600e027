/* eslint-disable no-console */
/* eslint-disable no-bitwise */
/* eslint-disable no-plusplus */
/* eslint-disable jsx-a11y/anchor-is-valid */
/* eslint-disable react/require-default-props */
import { useEffect, useState } from 'react';
import clsx from 'clsx';
import OtpInput from 'react-otp-input';
import styles from './styles.module.scss';

interface IComponentProps {
  onSubmitCode: (code: string) => void;
  isSubmitError?: boolean;
}

// eslint-disable-next-line @typescript-eslint/no-unused-vars
function toASCII(chars: string) {
  let ascii = '';
  try {
    for (let i = 0, l = chars.length; i < l; i++) {
      let c = chars[i].charCodeAt(0);

      // make sure we only convert half-full width char
      if (c >= 0xff00 && c <= 0xffef) {
        c = 0xff & (c + 0x20);
      }

      ascii += String.fromCharCode(c);
    }
  } catch (e) {
    console.log('e', e);
  }
  return ascii;
}

export default function SmsVerificationForm({ onSubmitCode, isSubmitError }: IComponentProps) {
  const [otp, setOtp] = useState('');

  useEffect(() => {
    try {
      if (otp?.length === 4) {
        onSubmitCode?.(otp);
      }
    } catch (e) {
      // eslint-disable-next-line no-console
      console.log('E', e);
    }
  }, [otp]);

  useEffect(() => {
    if (isSubmitError === true) {
      setOtp('');
      (document?.getElementsByClassName('SmsVerificationFormInput--1')?.[0] as HTMLElement)?.focus();
    }
  }, [isSubmitError]);

  return (
    <div className={styles.SmsVerificationForm}>
      <OtpInput
        containerStyle={{
          display: 'flex',
          justifyContent: 'center',
          gap: '16px',
          alignItems: 'center',
        }}
        inputStyle={{
          width: '56px',
          height: '56px',
        }}
        inputType="number"
        numInputs={4}
        onChange={setOtp}
        renderInput={(props, index) => (
          <input
            {...props}
            className={clsx('SmsVerificationFormInput', index === 0 ? 'SmsVerificationFormInput--1' : '')}
          />
        )}
        renderSeparator=""
        value={otp}
      />
    </div>
  );
}
