/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable react/require-default-props */
import React, { useEffect, useRef, useState } from 'react';

interface GoogleAdProps {
  adClient: string;
  adSlot: string;
  adFormat?: string;
  fullWidthResponsive?: boolean;
  style?: React.CSSProperties;
}

declare global {
  interface Window {
    adsbygoogle: any[];
  }
}

const GoogleAd: React.FC<GoogleAdProps> = ({ adClient, adSlot, adFormat, fullWidthResponsive = true, style }) => {
  const adRef = useRef<HTMLDivElement>(null);
  const [isAdLoaded, setIsAdLoaded] = useState(false);

  useEffect(() => {
    if (adRef.current) {
      const script = document.createElement('script');
      script.src = `https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=${adClient}`;
      script.async = true;
      script.crossOrigin = 'anonymous';
      adRef.current.appendChild(script);

      script.onload = () => {
        if (adRef.current) {
          const adElement = adRef.current.querySelector('ins.adsbygoogle');
          if (adElement) {
            window.adsbygoogle = window.adsbygoogle || [];
            window.adsbygoogle.push({});
            setIsAdLoaded(true);
          }
        }
      };

      return () => {
        if (adRef.current) {
          adRef.current.removeChild(script);
        }
      };
    }
    return () => {};
  }, [adClient, adRef.current]);

  if (!isAdLoaded) {
    return null;
  }

  return (
    <div ref={adRef} style={{ display: 'block', width: '100%', margin: 'auto', height: 'auto', ...style }}>
      <ins
        className="adsbygoogle"
        data-ad-client={adClient}
        {...(adFormat ? { 'data-ad-format': adFormat } : {})}
        data-ad-slot={adSlot}
        data-full-width-responsive={fullWidthResponsive}
        style={{ display: 'inline-block', ...style, margin: 'auto' }}
      />
    </div>
  );
};

export default GoogleAd;
