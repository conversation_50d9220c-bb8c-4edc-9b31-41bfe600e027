import React from 'react';
import { useRouter } from 'next/router';
import CButtonShadow from '../common/CButtonShadow';
import CModalWapper from '../common/CModalWapper';

export default function CreatorRoleModal({
  isOpen,
  onCancel,
  onOk,
  text,
  textOk,
  textCancel,
}: {
  isOpen: boolean;
  onCancel: () => void;
  onOk?: () => void;
  text?: string;
  textOk?: string;
  textCancel?: string;
}) {
  const router = useRouter();

  return (
    <CModalWapper isOpen={isOpen} onCancel={onCancel} textCancel={textCancel}>
      <div className=" py-[8px] ">
        <div className=" flex flex-col items-center">
          <p className="text-[14px] text-main-text leading-[22px] tracking-[0.39px]">
            {text ??
              'キャンペーンを作成するには、メールアドレス、パスワード、2段階認証をマイページで登録してください。'}
          </p>
          <div className="h-[24px]" />
          <div className="w-[236px] h-[53px]">
            <CButtonShadow
              onClick={() => {
                onCancel();
                if (onOk) {
                  onOk();
                } else {
                  router.push('/my-page');
                }
              }}
              title={textOk ?? 'マイページで設定する'}
              type="button"
            />
          </div>
        </div>
      </div>
    </CModalWapper>
  );
}

CreatorRoleModal.defaultProps = {
  text: undefined,
  textOk: undefined,
  textCancel: undefined,
  onOk: undefined,
};
