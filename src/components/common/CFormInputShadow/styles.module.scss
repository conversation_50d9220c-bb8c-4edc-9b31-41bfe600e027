.inputContainer {
  @apply w-full h-full relative;
}

.inputContainer :global(.inputContent) {
  @apply w-full h-full transition-all duration-200  border-[2px] flex items-center justify-center gap-[6px]  overflow-hidden font-medium text-[#333] text-[14px] font-notoSans leading-[21px]  bg-white absolute left-[-6px] top-[-6px] rounded-full border-[#333] outline-none  px-[24px] placeholder:text-gray-2 placeholder:font-medium focus:top-0 focus:left-0 hover:top-0 hover:left-0 focus:border-[#04AFAF];
}
.inputContainer :global(.inputContent.content-error) {
  border-color: red !important;
}

.inputContainer :global(.shadowContent) {
  @apply absolute left-0 top-0 h-full w-full border-[2px] bg-[#333] rounded-full border-[#333];
}

.inputContainer :global(.inputLabel) {
  @apply font-bold text-[14px];
}
.inputContainer :global(.inputLabel.label-error) {
  color: red !important;
}
