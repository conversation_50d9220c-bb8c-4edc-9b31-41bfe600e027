/* eslint-disable @typescript-eslint/no-explicit-any */
import ImgCrop, { ImgCropProps } from 'antd-img-crop';
import React, { useEffect, useState } from 'react';
import { Image as ImageAntd, Modal, Upload } from 'antd';
import type { RcFile, UploadFile, UploadProps } from 'antd/es/upload/interface';
import useWindowDimensions from '@/hooks/useWindowDimensions';
import { CropperProps } from 'react-easy-crop';
import styles from './index.module.scss';

const UploadButton = ({
  onChange,
  className,
  value,
  props,
  disabled,
  isSetCropSize,
  centeredModalPreview,
  isFullSize,
}: {
  value?: any;
  onChange?: (value: any) => void;
  className?: string;
  disabled?: boolean;
  isSetCropSize?: boolean;
  props?: Omit<ImgCropProps, 'children'>;
  centeredModalPreview?: boolean;
  isFullSize?: boolean;
}) => {
  const combinedClassName = [styles.customeButtonUpload, className].filter((e) => e).join(' ');
  const [previewOpen, setPreviewOpen] = useState(false);
  const [previewImage, setPreviewImage] = useState('');
  const [cropSizeAfterUpload, setCropSizeAfterUpload] = useState<{ cropperProps: CropperProps } | null>(null);
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  const { width, height } = useWindowDimensions();

  const getBase64 = (file: RcFile): Promise<string> =>
    new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => resolve(reader.result as string);
      reader.onerror = (error) => reject(error);
    });
  // 767
  const checkSizeImage = (file: RcFile, isCropSize?: boolean) => {
    const WINDOW_WIDTH_CROP = width > 536 ? 520 - 24 * 2 : width - 16 - 24 * 2;
    const WINDOW_HEIGHT_CROP = height * 0.4;
    try {
      const reader = new FileReader();
      reader.onload = (e) => {
        // eslint-disable-next-line prefer-const
        let img = new Image();
        img.onload = () => {
          if (isCropSize && img?.width && img?.height) {
            let heightSize = img.height / Math.ceil(img.height / 300);
            let widthSize = (heightSize * img.width) / img.height;
            if (widthSize > WINDOW_WIDTH_CROP || img.width / img.height > 1.7) {
              widthSize = WINDOW_WIDTH_CROP;
              heightSize = (img.height / img.width) * WINDOW_WIDTH_CROP;
            } else if (img.height / img.width > 1.7) {
              heightSize = WINDOW_HEIGHT_CROP;
              widthSize = (heightSize * img.width) / img.height;
            } else if (img.height < WINDOW_HEIGHT_CROP && img.width < WINDOW_HEIGHT_CROP) {
              heightSize = img.height;
              widthSize = img.width;
            } else if (heightSize < WINDOW_HEIGHT_CROP && widthSize < WINDOW_HEIGHT_CROP) {
              heightSize = WINDOW_HEIGHT_CROP;
              widthSize = (heightSize * img.width) / img.height;
            }
            const newCropSize = {
              cropperProps: {
                cropSize: { height: heightSize, width: widthSize },
              } as CropperProps,
            };
            setCropSizeAfterUpload(newCropSize);
          }
        };
        img.src = e?.target?.result as string;
      };
      reader.readAsDataURL(file as RcFile);
    } catch (error) {
      // eslint-disable-next-line no-console
      console.log('checkSizeImage error');
    }
  };

  const handleChange: UploadProps['onChange'] = ({ fileList: newlist, file }) => {
    if (file && file.status !== 'uploading') {
      checkSizeImage(file.originFileObj as RcFile);
      setFileList(newlist);
      onChange?.(newlist?.[0]?.originFileObj ?? null);
    }
  };

  const handlePreview = async (file: UploadFile) => {
    if (!file.url && !file.preview) {
      file.preview = await getBase64(file.originFileObj as RcFile);
    }
    setPreviewImage(file.url || (file.preview as string));
    setPreviewOpen(true);
  };
  const handleCancel = () => setPreviewOpen(false);

  useEffect(() => {
    if (value && fileList.length === 0) {
      setFileList([
        {
          uid: value.id,
          name: 'image.png',
          status: 'done',
          url: value.imageUrl,
          crossOrigin: 'anonymous',
        },
      ]);
    }
  }, [value, fileList]);

  return (
    <div className={`${combinedClassName}`}>
      <ImgCrop
        beforeCrop={(data) => {
          checkSizeImage(data, isSetCropSize);
        }}
        maxZoom={5}
        minZoom={0.1}
        modalCancel="キャンセル"
        modalClassName={styles.modalImgCrop}
        modalProps={{
          okButtonProps: { className: 'bg-[#333]' },
        }}
        modalTitle="画像修正"
        rotationSlider
        {...props}
        {...(cropSizeAfterUpload ?? {})}
      >
        <Upload
          // action="https://run.mocky.io/v3/435e224c-44fb-4773-9faf-380c5e6a2188"
          beforeUpload={(file: UploadFile, list) => {
            setFileList(list);
          }}
          disabled={disabled}
          fileList={fileList}
          listType="picture-card"
          onChange={handleChange}
          onPreview={handlePreview}
          onRemove={() => {
            setFileList([]);
            onChange?.(null);
          }}
          previewFile={isFullSize ? (getBase64 as any) : undefined}
        >
          {fileList.length > 0 ? '' : <span className="text-[16px] font-semibold text-white">画像を選択する</span>}
        </Upload>
      </ImgCrop>
      <Modal centered={centeredModalPreview} footer={null} onCancel={handleCancel} open={previewOpen}>
        <ImageAntd alt="example" crossOrigin="anonymous" preview={false} src={previewImage} width="100%" />
      </Modal>
    </div>
  );
};
UploadButton.defaultProps = {
  className: undefined,
  props: undefined,
  onChange: undefined,
  value: undefined,
  disabled: false,
  isSetCropSize: false,
  centeredModalPreview: false,
  isFullSize: false,
};
export default UploadButton;
