@import '../../../styles/globals.scss';
.customeButtonUpload {
  :global {
    .ant-upload.ant-upload-select {
      width: 100% !important;
      height: 48px !important;
      margin: 0 !important;
      margin-inline-end: 0 !important;
      border-radius: 6px !important;
      border: none !important;
      background-color: #333 !important;
      &:hover {
        opacity: 0.8;
      }
    }
    .reactEasyCrop_CropArea {
      width: 300px !important;
      height: 300px !important;
    }
    .ant-upload-wrapper .ant-upload-list.ant-upload-list-picture-card .ant-upload-list-item {
      padding: 0;
      border-radius: 0;
      border: 2px solid #2d3648;
    }
    .ant-upload-wrapper.ant-upload-picture-card-wrapper
      .ant-upload-list.ant-upload-list-picture-card
      .ant-upload-list-item::before {
      width: 100%;
      height: 100%;
    }
    .ant-upload-wrapper.ant-upload-picture-card-wrapper
      .ant-upload-list.ant-upload-list-picture-card
      .ant-upload-list-item-container {
      min-height: 216px;
      min-width: 467px;
      @include mobile {
        // min-height: 216px;
        // width: calc(100px - 60px);
        min-width: 200px;
        width: auto;
      }
    }
  }
}
