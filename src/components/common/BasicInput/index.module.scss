.customInput {
  width: 100%;
  :global {
    .ant-input,
    .ant-input-number {
      width: 100%;
      min-height: 48px;
      max-height: 53px;
      border-width: 2px;
      border-style: solid;
      border-color: #333;
      border-radius: 6px;
      padding: 16px 48px 16px 24px;
      font-weight: 400;
      font-size: 14px;
      line-height: 0;
    }
    .ant-input:hover,
    .ant-input-number:hover,
    .ant-input-number:focus,
    .ant-input:focus,
    .ant-input-number:focus-within {
      border-width: 2px;
      border-color: #333;
    }
    .ant-input-status-error:not(.ant-input-disabled):not(.ant-input-borderless).ant-input {
      border-width: 2px;
      // color: #ff0000;
    }
    .ant-input-status-error:not(.ant-input-disabled):not(.ant-input-borderless).ant-input:hover {
      border-width: 2px;
      border-color: #ff0000;
    }

    .ant-input-number-outlined.ant-input-number-status-error:not(.ant-input-number-disabled) {
      border-width: 2px;
      // color: #ff0000;
    }
    .ant-input-number-outlined.ant-input-number-status-error:not(.ant-input-number-disabled):hover {
      border-width: 2px;
      border-color: #ff0000;
    }

    .ant-input-number-input-wrap {
      width: 100%;
    }
    .ant-input-number-handler-wrap {
      display: none;
    }
    .ant-input-number .ant-input-number-input {
      padding: 0;
      line-height: normal;
    }
  }
}
.inputLabel {
  width: 100%;
  ::-ms-input-placeholder {
    /* Edge 12-18 */
    color: #aaa;
    font-size: 14px;
    font-weight: 500;
  }

  ::placeholder {
    color: #aaa;
    font-size: 14px;
    font-weight: 500;
  }
  :global {
    .ant-form-item .ant-form-item-explain-error {
      color: #ff0000 !important;
      font-size: 12px;
      margin-bottom: 24px;
    }
    .ant-form-item-with-help .ant-form-item-explain {
      /* height: 38px; */
      margin-top: 10px;
    }
  }
}
