.customPagination {
  :global {
    .ant-pagination .ant-pagination-item {
      font-family: var(--font-noto-san-jp);
      font-size: 16px;
      font-weight: 500;

      a {
        color: #333;
      }
    }
    .ant-pagination .ant-pagination-item-active {
      font-size: 16px;
      font-weight: 700;
      border: none;
      background: transparent;
      a {
        color: #333;
        font-weight: 700;
        text-decoration: underline;
        text-underline-offset: 8px;
        text-decoration-thickness: 2px;
      }
    }
    .ant-pagination-total-text {
      font-size: 13px;
      font-family: var(--font-noto-san-jp);
      font-weight: 700;
    }
    .ant-pagination .ant-pagination-prev {
      margin-inline-end: 16px;
    }
    .ant-pagination .ant-pagination-next {
      margin-inline-start: 16px;
    }
    .ant-pagination-total-text {
      margin-inline-end: 41px;
      padding-top: 6px;
    }
    .ant-table-wrapper .ant-table-pagination.ant-pagination {
      margin-top: 56px;
    }
  }
}
