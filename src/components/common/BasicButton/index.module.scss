.btnDisabled {
  button:hover {
    border-color: rgba(45, 54, 72, 0.2) !important;
    color: #ccc !important;
  }
  button {
    background-color: white !important;
    border-radius: 6px !important;
    height: 100% !important;
    width: 100% !important;
    color: #ccc !important;
    font-size: 16px;
    font-weight: 700;
    // border: 2px solid rgba(45, 54, 72, 0.2) !important;
    opacity: 0.8;
    cursor: not-allowed;
  }
}
.btnDefault {
  button:hover {
    border-color: rgba(45, 54, 72, 0.2) !important;
    color: white !important;
  }
  button {
    background-color: #2d3648 !important;
    border-radius: 6px !important;
    height: 100% !important;
    width: 100% !important;
    color: white;
    font-size: 16px;
    font-weight: 700;
  }
}
.btnPrimary {
  button:hover {
    border-color: rgba(45, 54, 72, 0.2) !important;
    color: #2d3648 !important;
  }
  button {
    background-color: white !important;
    border-radius: 6px !important;
    height: 100% !important;
    width: 100% !important;
    color: #2d3648;
    font-size: 16px;
    font-weight: 700;
    border: 2px solid #2d3648 !important;
  }
}
