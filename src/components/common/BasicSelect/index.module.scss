.defaultSelect {
  width: 100%;
  :global {
    .ant-select-single {
      height: 53px;
      width: 100%;
    }
    .ant-select-single .ant-select-selector {
      border-radius: 4px;
      border-color: #333;
    }
    .ant-select:not(.ant-select-disabled):not(.ant-select-customize-input):not(.ant-pagination-size-changer):hover
      .ant-select-selector {
      border-color: #333 !important;
      padding: 0px 24px;
      border-width: 2px;
      box-shadow: none;
    }
    .ant-select-status-error:not(.ant-select-disabled):not(.ant-select-customize-input):not(
        .ant-pagination-size-changer
      )
      .ant-select-selector {
      border-width: 2px;
    }
    // .ant-select-selector {
    //   border: 0px !important;
    // }
    .ant-select-selector:hover {
      border-color: #333;
      border-width: 2px !important;
    }
    // .ant-select-arrow {
    //   padding-right: 0;
    // }
    .ant-select-selection-item {
      font-size: 14px;
      color: #333;
      font-weight: 400;
    }
    .ant-select:not(.ant-select-customize-input) .ant-select-selector {
      padding: 0 24px;
    }
    input {
      font-size: 16px;
    }
    .ant-select-selection-placeholder {
      font-size: 14px !important;
      font-weight: 400;
      color: #aaaaaa;
    }
    .ant-select:not(.ant-select-customize-input) .ant-select-selector {
      border-width: 2px;
    }
    .ant-select .ant-select-arrow {
      inset-inline-end: 24px;
    }
    // .ant-select-status-error:not(.ant-select-disabled):not(.ant-select-customize-input):not(.ant-pagination-size-changer) .ant-select-selector{

    // }
    .ant-select-focused:where(.css-dev-only-do-not-override-qgg3xn).ant-select:not(.ant-select-disabled):not(
        .ant-select-customize-input
      ):not(.ant-pagination-size-changer)
      .ant-select-selector {
      border-color: var(--primary);
      box-shadow: var(--primary);
    }
  }
}
.customPopup {
  :global {
    padding: 0 !important;

    .ant-select-item {
      padding: 12px 40px !important;
      height: 41px;
      // display: flex;
      // align-items: center;
      // justify-content: center;
      // text-align: center;
      color: #333;
      font-size: 14px;
      line-height: 20px;
      font-weight: 700;
    }

    .ant-select-item.ant-select-item-option {
      line-height: var(--input-element-height);
      height: var(--input-element-height);
    }

    .ant-select-item-option-content {
      align-self: center;
    }

    // .ant-select-item.ant-select-item-option.ant-select-item-option-active {
    //   background-color: #f2faf8 !important;
    //   // border: 1px !important;
    //   // border-color: #009b75 !important;
    // }
    .ant-select-item.ant-select-item-option.ant-select-item-option-selected {
      background-color: #f2f2f2 !important;
      // border: 1px solid #009b75 !important;
      box-shadow: #f2f2f2;
      // border-radius: 5px;
      // border: 1px !important;
      // border-color: #009b75 !important;
    }

    @media screen and (max-width: 767.999px) {
      .ant-select-item {
        padding: 12px 20px !important;
      }
    }
  }
}
.customSelectShadow {
  width: 100%;
  height: 50px;

  :global {
  }
}
