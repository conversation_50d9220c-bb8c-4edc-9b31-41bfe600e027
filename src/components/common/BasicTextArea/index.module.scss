.customeTextArea {
  width: 100%;
  :global {
    .ant-input {
      // height: 60px;
      border-width: 2px;
      border-style: solid;
      border-color: #333;
      border-radius: 6px;
      padding: 16px 24px;
      font-weight: 400;
      font-size: 16px;
    }
    .ant-input:hover,
    .ant-input:focus {
      border-width: 2px;
      border-color: #333;
    }
    .ant-input-status-error:not(.ant-input-disabled):not(.ant-input-borderless).ant-input {
      border-width: 2px;
    }
    .ant-input-status-error:not(.ant-input-disabled):not(.ant-input-borderless).ant-input:hover {
      border-width: 2px;
      border-color: #ff4d4f;
    }
  }
}
