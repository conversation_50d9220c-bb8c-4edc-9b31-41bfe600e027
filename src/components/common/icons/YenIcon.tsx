import React from 'react';

interface IComponentProps {
  className?: string;
  color?: string;
}

export default function YenIcon({ className, color }: IComponentProps) {
  return (
    <svg
      className={className}
      fill="none"
      height="14"
      viewBox="0 0 10 14"
      width="10"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M4.97266 5.62305L7.76172 1.4668C8.03516 1.05664 8.58203 0.947266 8.96484 1.2207C9.375 1.49414 9.48438 2.04102 9.21094 2.42383L6.61328 6.30664H8.0625C8.52734 6.30664 8.9375 6.7168 8.9375 7.18164C8.9375 7.67383 8.52734 8.05664 8.0625 8.05664H5.875V8.93164H8.0625C8.52734 8.93164 8.9375 9.3418 8.9375 9.80664C8.9375 10.2988 8.52734 10.6816 8.0625 10.6816H5.875V12.4316C5.875 12.9238 5.46484 13.3066 4.97266 13.3066C4.50781 13.3066 4.09766 12.9238 4.09766 12.4316V10.6816H1.91016C1.44531 10.6816 1.03516 10.2988 1.03516 9.80664C1.03516 9.3418 1.44531 8.93164 1.91016 8.93164H4.09766V8.05664H1.91016C1.44531 8.05664 1.03516 7.67383 1.03516 7.18164C1.03516 6.7168 1.44531 6.30664 1.91016 6.30664H3.35938L0.761719 2.42383C0.488281 2.04102 0.597656 1.49414 1.00781 1.2207C1.39062 0.947266 1.9375 1.05664 2.21094 1.4668L4.97266 5.62305Z"
        fill={color}
      />
    </svg>
  );
}
YenIcon.defaultProps = {
  className: 'text-[#333]',
  color: '#333333',
};
