import React from 'react';

export default function LineIcon() {
  return (
    <div className="w-[40px] h-[40px]">
      <svg
        fill="none"
        height="100%"
        preserveAspectRatio="xMidYMid meet"
        viewBox="0 0 40 40"
        width="100%"
        xmlns="http://www.w3.org/2000/svg"
      >
        <rect fill="white" height="38" rx="3" stroke="#333333" strokeWidth="2" width="38" x="1" y="1" />
        <path
          d="M29.4109 18.6653C29.4109 14.4387 25.1737 11 19.9652 11C14.7568 11 10.5195 14.4387 10.5195 18.6653C10.5195 22.4545 13.8803 25.6276 18.4194 26.2279C18.7266 26.2943 19.1454 26.4306 19.2516 26.6936C19.3473 26.9326 19.3136 27.3062 19.2826 27.5479C19.2826 27.5479 19.172 28.2146 19.1481 28.3562C19.1065 28.5953 18.9586 29.2903 19.9661 28.8653C20.9745 28.4403 25.4057 25.6621 27.387 23.3815C28.7558 21.88 29.4118 20.3572 29.4118 18.6653H29.4109Z"
          fill="black"
        />
        <path
          d="M26.2598 21.1045H23.6064C23.5063 21.1045 23.4258 21.0239 23.4258 20.9238V20.9212V16.8061V16.8017C23.4258 16.7017 23.5063 16.6211 23.6064 16.6211H26.2598C26.3589 16.6211 26.4404 16.7025 26.4404 16.8017V17.4719C26.4404 17.572 26.3598 17.6525 26.2598 17.6525H24.4563V18.3484H26.2598C26.3589 18.3484 26.4404 18.4298 26.4404 18.529V19.1992C26.4404 19.2992 26.3598 19.3798 26.2598 19.3798H24.4563V20.0757H26.2598C26.3589 20.0757 26.4404 20.1571 26.4404 20.2563V20.9265C26.4404 21.0265 26.3598 21.1071 26.2598 21.1071V21.1045Z"
          fill="white"
        />
        <path
          d="M16.456 21.1053C16.5551 21.1053 16.6366 21.0248 16.6366 20.9247V20.2545C16.6366 20.1554 16.5551 20.0739 16.456 20.0739H14.6525V16.8017C14.6525 16.7025 14.5711 16.6211 14.4719 16.6211H13.8017C13.7017 16.6211 13.6211 16.7017 13.6211 16.8017V20.9212V20.9247C13.6211 21.0248 13.7017 21.1053 13.8017 21.1053H16.4551H16.456Z"
          fill="white"
        />
        <path
          d="M18.0531 16.6211H17.3837C17.284 16.6211 17.2031 16.702 17.2031 16.8017V20.9239C17.2031 21.0236 17.284 21.1045 17.3837 21.1045H18.0531C18.1528 21.1045 18.2337 21.0236 18.2337 20.9239V16.8017C18.2337 16.702 18.1528 16.6211 18.0531 16.6211Z"
          fill="white"
        />
        <path
          d="M22.6063 16.6211H21.9361C21.8361 16.6211 21.7555 16.7017 21.7555 16.8017V19.2497L19.8698 16.7025C19.8653 16.6963 19.86 16.6902 19.8556 16.684C19.8556 16.684 19.8556 16.684 19.8547 16.6831C19.8512 16.6795 19.8476 16.6751 19.8441 16.6716C19.8432 16.6707 19.8414 16.6698 19.8405 16.6689C19.837 16.6662 19.8343 16.6636 19.8308 16.6609C19.829 16.66 19.8273 16.6583 19.8255 16.6574C19.8228 16.6547 19.8193 16.653 19.8158 16.6512C19.814 16.6503 19.8122 16.6485 19.8104 16.6477C19.8069 16.6459 19.8042 16.6441 19.8007 16.6423C19.7989 16.6415 19.7972 16.6406 19.7954 16.6397C19.7918 16.6379 19.7883 16.6361 19.7848 16.6353C19.783 16.6353 19.7812 16.6335 19.7786 16.6335C19.775 16.6326 19.7715 16.6308 19.7679 16.6299C19.7662 16.6299 19.7635 16.6291 19.7617 16.6282C19.7582 16.6282 19.7547 16.6264 19.7511 16.6255C19.7485 16.6255 19.7458 16.6255 19.7432 16.6246C19.7396 16.6246 19.737 16.6237 19.7334 16.6237C19.7299 16.6237 19.7272 16.6237 19.7237 16.6237C19.7219 16.6237 19.7193 16.6237 19.7175 16.6237H19.0517C18.9525 16.6237 18.8711 16.7043 18.8711 16.8044V20.9265C18.8711 21.0257 18.9517 21.1071 19.0517 21.1071H19.7219C19.8219 21.1071 19.9025 21.0265 19.9025 20.9265V18.4785L21.7909 21.0292C21.8042 21.0478 21.8202 21.0628 21.8379 21.0744C21.8379 21.0744 21.8396 21.0752 21.8396 21.0761C21.8432 21.0788 21.8467 21.0806 21.8512 21.0832C21.8529 21.0841 21.8547 21.085 21.8565 21.0859C21.8591 21.0876 21.8627 21.0885 21.8653 21.0903C21.868 21.0921 21.8715 21.0929 21.8742 21.0938C21.8759 21.0938 21.8777 21.0956 21.8795 21.0956C21.8839 21.0974 21.8875 21.0983 21.8919 21.0991C21.8919 21.0991 21.8936 21.0991 21.8945 21.0991C21.9096 21.1027 21.9255 21.1053 21.9415 21.1053H22.6072C22.7064 21.1053 22.7878 21.0248 22.7878 20.9247V16.8026C22.7878 16.7034 22.7073 16.622 22.6072 16.622L22.6063 16.6211Z"
          fill="white"
        />
      </svg>
    </div>
  );
}
