import React from 'react';

interface IComponentProps {
  className?: string;
}

export default function CalendarIcon({ className }: IComponentProps) {
  return (
    <svg
      className={className}
      fill="none"
      height="15"
      viewBox="0 0 14 15"
      width="14"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M4.92188 8.05664H3.82812C3.63672 8.05664 3.5 7.91992 3.5 7.72852V6.63477C3.5 6.4707 3.63672 6.30664 3.82812 6.30664H4.92188C5.08594 6.30664 5.25 6.4707 5.25 6.63477V7.72852C5.25 7.91992 5.08594 8.05664 4.92188 8.05664ZM7.875 7.72852C7.875 7.91992 7.71094 8.05664 7.54688 8.05664H6.45312C6.26172 8.05664 6.125 7.91992 6.125 7.72852V6.63477C6.125 6.4707 6.26172 6.30664 6.45312 6.30664H7.54688C7.71094 6.30664 7.875 6.4707 7.875 6.63477V7.72852ZM10.5 7.72852C10.5 7.91992 10.3359 8.05664 10.1719 8.05664H9.07812C8.88672 8.05664 8.75 7.91992 8.75 7.72852V6.63477C8.75 6.4707 8.88672 6.30664 9.07812 6.30664H10.1719C10.3359 6.30664 10.5 6.4707 10.5 6.63477V7.72852ZM7.875 10.3535C7.875 10.5449 7.71094 10.6816 7.54688 10.6816H6.45312C6.26172 10.6816 6.125 10.5449 6.125 10.3535V9.25977C6.125 9.0957 6.26172 8.93164 6.45312 8.93164H7.54688C7.71094 8.93164 7.875 9.0957 7.875 9.25977V10.3535ZM5.25 10.3535C5.25 10.5449 5.08594 10.6816 4.92188 10.6816H3.82812C3.63672 10.6816 3.5 10.5449 3.5 10.3535V9.25977C3.5 9.0957 3.63672 8.93164 3.82812 8.93164H4.92188C5.08594 8.93164 5.25 9.0957 5.25 9.25977V10.3535ZM10.5 10.3535C10.5 10.5449 10.3359 10.6816 10.1719 10.6816H9.07812C8.88672 10.6816 8.75 10.5449 8.75 10.3535V9.25977C8.75 9.0957 8.88672 8.93164 9.07812 8.93164H10.1719C10.3359 8.93164 10.5 9.0957 10.5 9.25977V10.3535ZM13.125 3.24414V12.8691C13.125 13.6074 12.5234 14.1816 11.8125 14.1816H2.1875C1.44922 14.1816 0.875 13.6074 0.875 12.8691V3.24414C0.875 2.5332 1.44922 1.93164 2.1875 1.93164H3.5V0.509766C3.5 0.345703 3.63672 0.181641 3.82812 0.181641H4.92188C5.08594 0.181641 5.25 0.345703 5.25 0.509766V1.93164H8.75V0.509766C8.75 0.345703 8.88672 0.181641 9.07812 0.181641H10.1719C10.3359 0.181641 10.5 0.345703 10.5 0.509766V1.93164H11.8125C12.5234 1.93164 13.125 2.5332 13.125 3.24414ZM11.8125 12.7051V4.55664H2.1875V12.7051C2.1875 12.8145 2.24219 12.8691 2.35156 12.8691H11.6484C11.7305 12.8691 11.8125 12.8145 11.8125 12.7051Z"
        fill="currentColor"
      />
    </svg>
  );
}
CalendarIcon.defaultProps = {
  className: 'text-[#333]',
};
