import React from 'react';

interface IArrowProps {
  className?: string;
}

export default function Copy({ className }: IArrowProps) {
  return (
    <svg
      className={className}
      fill="none"
      height="23"
      viewBox="0 0 19 23"
      width="19"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M16.9844 20.668V6.65234H6.01562V20.668H16.9844ZM16.9844 4.68359C17.5156 4.68359 17.9844 4.88672 18.3906 5.29297C18.7969 5.66797 19 6.12109 19 6.65234V20.668C19 21.1992 18.7969 21.668 18.3906 22.0742C17.9844 22.4805 17.5156 22.6836 16.9844 22.6836H6.01562C5.48438 22.6836 5.01562 22.4805 4.60938 22.0742C4.20312 21.668 4 21.1992 4 20.668V6.65234C4 6.12109 4.20312 5.66797 4.60938 5.29297C5.01562 4.88672 5.48438 4.68359 6.01562 4.68359H16.9844ZM13.9844 0.652344V2.66797H1.98438V16.6836H0.015625V2.66797C0.015625 2.13672 0.203125 1.66797 0.578125 1.26172C0.984375 0.855469 1.45312 0.652344 1.98438 0.652344H13.9844Z"
        fill="#5D5A88"
      />
    </svg>
  );
}
Copy.defaultProps = {
  className: 'text-[#333]',
};
