.customTable {
  position: relative;
  :global {
    .ant-table-wrapper
      .ant-table-thead
      > tr
      > th:not(:last-child):not(.ant-table-selection-column):not(.ant-table-row-expand-icon-cell):not(
        [colspan]
      )::before {
      display: none;
    }
    .ant-table-wrapper .ant-table-thead > tr > th {
      background: #fff;
      border-bottom: 1px solid #aaa;
      padding: 19px 24px;
    }

    .ant-table-wrapper .ant-table-tbody > tr > td {
      border: none;
      padding: 21px 24px;
    }
    .ant-table-wrapper .ant-table {
      border-radius: 8px;
    }
    .ant-pagination .ant-pagination-item {
      font-family: var(--font-noto-san-jp);
      font-size: 16px;
      font-weight: 500;

      a {
        color: #333;
      }
    }
    .ant-pagination .ant-pagination-item-active {
      font-size: 16px;
      font-weight: 700;
      border: none;
      background: transparent;
      a {
        color: #333;
        font-weight: 700;
        text-decoration: underline;
        text-underline-offset: 8px;
        text-decoration-thickness: 2px;
      }
    }
    .ant-pagination-total-text {
      font-size: 13px;
      font-family: var(--font-noto-san-jp);
      font-weight: 700;
    }
    .ant-pagination .ant-pagination-prev {
      margin-inline-end: 34px;
    }
    .ant-pagination .ant-pagination-next {
      margin-inline-start: 34px;
    }
    .ant-pagination-total-text {
      margin-inline-end: 41px;
      padding-top: 6px;
    }
    .ant-table-wrapper .ant-table-pagination.ant-pagination {
      margin-top: 56px;
    }
  }
}


@media screen and (max-width: 600px) {
  .customTable {
    position: relative;
    :global {
      .ant-pagination .ant-pagination-prev {
        margin-inline-end: 24px;
      }
      .ant-pagination .ant-pagination-next {
        margin-inline-start: 24px;
      }
      .ant-pagination.ant-pagination-simple .ant-pagination-simple-pager{
        height: 40px !important;
      }
    }
  }
  
}