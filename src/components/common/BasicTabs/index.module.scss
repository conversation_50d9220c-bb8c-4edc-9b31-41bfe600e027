@import '../../../styles/globals.scss';
.customeTabs {
  :global {
    .ant-tabs .ant-tabs-tab + .ant-tabs-tab {
      margin: 0 !important;
      font-family: var(--font-noto-san-jp) !important;
    }
    .ant-table-wrapper .ant-table {
      font-family: var(--font-noto-san-jp) !important;
    }
    .ant-tabs-nav .ant-tabs-nav-list {
      width: 100%;
      @include mobile {
        gap: 24px;
        width: auto;
      }
    }

    .ant-tabs .ant-tabs-tab {
      font-size: 16px;
      color: #2d3648 !important;
      font-weight: 700;
      width: 100%;
    }
    .ant-tabs .ant-tabs-ink-bar {
      background: #2d3648 !important;
      height: 3px;
    }
    .ant-tabs .ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn {
      color: #2d3648 !important;
      font-size: 16px;
      font-weight: 700;
      text-shadow: none;
    }
    .ant-tabs-nav::before {
      border-bottom: 1px solid #333 !important;
    }
    .ant-tabs > .ant-tabs-nav .ant-tabs-nav-operations,
    .ant-tabs > div > .ant-tabs-nav .ant-tabs-nav-operations {
      display: none;
    }
    .ant-tabs .ant-tabs-tab.ant-tabs-tab-disabled {
      color: #aaa !important;
    }
  }
}
