.customeDatePicker {
  :global {
    .ant-picker-focused.ant-picker {
      border-color: var(--primary);
      box-shadow: none;
    }

    .ant-picker {
      height: 53px;
      border-width: 2px;
      border-style: solid;
      border-color: #333;
      border-radius: 6px;
      padding: 16px 24px;
      font-weight: 500;
      font-size: 16px;
      width: 100%;
    }
    .ant-picker:hover {
      border-color: var(--primary);
      border-width: 2px;
    }
    // .ant-picker-suffix {
    //   display: none;
    // }
    .ant-picker-clear {
      display: none;
    }
    .ant-picker-input {
      input {
        text-align: left;
        font-size: 16px;
        color: var(--primary-text);
      }
    }
  }
}
.customPopup {
  :global {
    .ant-btn-primary {
      background-color: #333;
    }
    @media screen and (max-width: 425px) {
      .ant-picker-datetime-panel {
        display: flex;
        flex-direction: column !important;
        max-height: 400px !important ;
        overflow-y: scroll !important;
        .ant-picker-date-panel {
          max-width: 268px !important;
        }
        .ant-picker-date-panel .ant-picker-body {
          padding-left: 8px !important;
          padding-right: 8px !important;
        }
      }
    }
  }
}
