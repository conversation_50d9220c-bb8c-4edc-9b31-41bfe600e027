import { DatePicker, DatePickerProps } from 'antd';
import React from 'react';
import styles from './index.module.scss';

function BasicDatePicker(props: DatePickerProps) {
  return (
    <div className={styles.customeDatePicker}>
      <DatePicker
        changeOnBlur
        popupClassName={styles.customPopup}
        suffixIcon={
          <svg fill="none" height="15" viewBox="0 0 14 15" width="14" xmlns="http://www.w3.org/2000/svg">
            <g clipPath="url(#clip0_2149_3815)">
              <path
                d="M5.04688 8.375H3.95312C3.76172 8.375 3.625 8.23828 3.625 8.04688V6.95312C3.625 6.78906 3.76172 6.625 3.95312 6.625H5.04688C5.21094 6.625 5.375 6.78906 5.375 6.95312V8.04688C5.375 8.23828 5.21094 8.375 5.04688 8.375ZM8 8.04688C8 8.23828 7.83594 8.375 7.67188 8.375H6.57812C6.38672 8.375 6.25 8.23828 6.25 8.04688V6.95312C6.25 6.78906 6.38672 6.625 6.57812 6.625H7.67188C7.83594 6.625 8 6.78906 8 6.95312V8.04688ZM10.625 8.04688C10.625 8.23828 10.4609 8.375 10.2969 8.375H9.20312C9.01172 8.375 8.875 8.23828 8.875 8.04688V6.95312C8.875 6.78906 9.01172 6.625 9.20312 6.625H10.2969C10.4609 6.625 10.625 6.78906 10.625 6.95312V8.04688ZM8 10.6719C8 10.8633 7.83594 11 7.67188 11H6.57812C6.38672 11 6.25 10.8633 6.25 10.6719V9.57812C6.25 9.41406 6.38672 9.25 6.57812 9.25H7.67188C7.83594 9.25 8 9.41406 8 9.57812V10.6719ZM5.375 10.6719C5.375 10.8633 5.21094 11 5.04688 11H3.95312C3.76172 11 3.625 10.8633 3.625 10.6719V9.57812C3.625 9.41406 3.76172 9.25 3.95312 9.25H5.04688C5.21094 9.25 5.375 9.41406 5.375 9.57812V10.6719ZM10.625 10.6719C10.625 10.8633 10.4609 11 10.2969 11H9.20312C9.01172 11 8.875 10.8633 8.875 10.6719V9.57812C8.875 9.41406 9.01172 9.25 9.20312 9.25H10.2969C10.4609 9.25 10.625 9.41406 10.625 9.57812V10.6719ZM13.25 3.5625V13.1875C13.25 13.9258 12.6484 14.5 11.9375 14.5H2.3125C1.57422 14.5 1 13.9258 1 13.1875V3.5625C1 2.85156 1.57422 2.25 2.3125 2.25H3.625V0.828125C3.625 0.664062 3.76172 0.5 3.95312 0.5H5.04688C5.21094 0.5 5.375 0.664062 5.375 0.828125V2.25H8.875V0.828125C8.875 0.664062 9.01172 0.5 9.20312 0.5H10.2969C10.4609 0.5 10.625 0.664062 10.625 0.828125V2.25H11.9375C12.6484 2.25 13.25 2.85156 13.25 3.5625ZM11.9375 13.0234V4.875H2.3125V13.0234C2.3125 13.1328 2.36719 13.1875 2.47656 13.1875H11.7734C11.8555 13.1875 11.9375 13.1328 11.9375 13.0234Z"
                fill="#333333"
              />
            </g>
            <defs>
              <clipPath id="clip0_2149_3815">
                <rect fill="white" height="14" transform="translate(0 0.5)" width="14" />
              </clipPath>
            </defs>
          </svg>
        }
        {...props}
      />
    </div>
  );
}

export default BasicDatePicker;
