import React, { useEffect } from 'react';
import { useMediaQuery } from 'usehooks-ts';
import CButtonClassic from '../CButtonClassic';
import XCricleIcon from '../icons/XCricleIcon';
import XMarkIcon from '../icons/XMarkIcon';

interface IComponentProps {
  isOpen: boolean;
  onCancel: () => void;
  modalWidth?: number;
  textCancel?: string;
  isHiddenBottomCloseBtn?: boolean;
  children: React.ReactNode;
}

export default function CModalWapper({
  isOpen,
  onCancel,
  modalWidth,
  isHiddenBottomCloseBtn,
  children,
  textCancel,
}: IComponentProps) {
  const matchesXS = useMediaQuery('(max-width: 350px)');
  useEffect(() => {
    const scrollBarWidth = window.innerWidth - document.body.clientWidth;
    if (isOpen) {
      document.body.classList.add('fixed-page');
      document.body.style.paddingRight = `${scrollBarWidth}px`;
    } else {
      document.body.classList.remove('fixed-page');
      document.body.style.paddingRight = 'unset';
    }
  }, [isOpen]);

  return (
    <div>
      {isOpen && (
        <div className="w-screen h-screen top-0 left-0 right-0 bottom-0 fixed z-[999999]">
          <div
            aria-hidden
            className="w-screen h-screen top-0 left-0 right-0 bottom-0 fixed z-[999999] bg-[rgba(51,_51,_51,_0.9)] pt-[30px] pb-[10px] overflow-y-auto flex justify-center "
            onClick={(e) => {
              onCancel();
              e.stopPropagation();
              e.preventDefault();
            }}
          >
            <div
              style={{
                width: `${matchesXS ? 300 : modalWidth}px`,
                animation: 'modalDropTop 0.3s linear',
              }}
            >
              <div className="flex justify-end">
                <div aria-hidden="true" className="cursor-pointer" onClick={onCancel}>
                  <XCricleIcon />
                </div>
              </div>
              <div className="h-[24px]" />
              <div
                aria-hidden
                className="bg-white rounded-[16px]  py-[48px] px-[24px]"
                onClick={(e) => {
                  e.stopPropagation();
                }}
              >
                {children}
              </div>
              <div className="h-[40px]" />
              {isHiddenBottomCloseBtn !== true && (
                <div className="flex justify-center">
                  <div className="w-[139px] h-[47px]">
                    <CButtonClassic
                      customClassName="!bg-[#FFF] !text-main-text !text-[14px]
            !font-bold"
                      onClick={onCancel}
                      title={textCancel ?? '閉じる'}
                      withIcon={{
                        position: 'left',
                        icon: <XMarkIcon />,
                      }}
                    />
                  </div>
                </div>
              )}

              <div className="h-[100px]" />
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
CModalWapper.defaultProps = {
  modalWidth: 343,
  isHiddenBottomCloseBtn: false,
  textCancel: undefined,
};
