/* eslint-disable react/jsx-no-useless-fragment */
import { DataMaintenanceItem, useGetMaintenanceQuery } from '@/redux/endpoints/maintenance';
import { RootState } from '@/redux/store';
import dayjs from 'dayjs';
import { useRouter } from 'next/router';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { useSelector } from 'react-redux';
import CreatorRoleModal from '../CreatorRoleFeedbackModal/CreatorRoleModal';
import Loading from '../Loading';

export default function UserRoleWapper({ children }: { children: React.ReactElement }) {
  const [isOpenCreatorRoleModal, setIsOpenCreatorRoleModal] = useState(false);
  const { data: campaignDetailTasks, isFetching } = useGetMaintenanceQuery({});
  const [maintenance, setMaintenance] = useState<DataMaintenanceItem | undefined>();
  const router = useRouter();
  const { user } = useSelector((state: RootState) => state.auth);

  useEffect(() => {
    if (campaignDetailTasks?.data?.data) {
      const activeMaintenance = campaignDetailTasks?.data?.data?.find((item) => item);
      setMaintenance(activeMaintenance);
    }
  }, [campaignDetailTasks]);

  const onThrowRouterError = () => {
    // Throwing an actual error class trips the Next.JS 500 Page, this string literal does not.
    // eslint-disable-next-line no-throw-literal, @typescript-eslint/no-throw-literal
    throw ' 👍 Abort route change due to user not enough condition to view page site. Triggered by UserRoleWapper.tsx. Please ignore this error.';
  };

  const isPageMaintenance = useMemo(() => {
    if (isFetching) {
      return undefined;
    }
    if (
      maintenance &&
      dayjs(maintenance.endAt).unix() > dayjs().unix() &&
      dayjs(maintenance.startAt).unix() <= dayjs().unix()
    ) {
      return true;
    }
    return false;
  }, [maintenance, isFetching]);

  const onStopRouterEvent = useCallback(() => {
    router.events.emit('routeChangeError');
    onThrowRouterError();
  }, [router]);

  useEffect(() => {
    const start = (url) => {
      if (url?.startsWith('/campaign-creator') && (user?.twoFactorMethod === 'NONE' || user?.emailId === null)) {
        setIsOpenCreatorRoleModal(true);
        onStopRouterEvent();
      }
    };
    const end = () => {};
    router.events.on('routeChangeStart', start);
    router.events.on('routeChangeComplete', end);
    router.events.on('routeChangeError', end);

    return () => {
      router.events.off('routeChangeStart', start);
      router.events.off('routeChangeComplete', end);
      router.events.off('routeChangeError', end);
    };
  });

  useEffect(() => {
    let isAdminPage = false;
    if (
      router.pathname?.startsWith('/admin') ||
      router.pathname?.startsWith('/inquiry') ||
      router.pathname?.startsWith('/privacy-policy') ||
      router.pathname?.startsWith('/specified-commercial-transactions-law') ||
      router.pathname?.startsWith('/terms-of-service')
    ) {
      isAdminPage = true;
    }
    const shouldRedirect = isPageMaintenance && maintenance !== undefined && !isAdminPage;
    const isInMaintenancePage = router.pathname === '/maintenance-period';
    if (isAdminPage) {
      // router admin banner detail
      if (router.pathname?.startsWith('/admin/banner/detail')) {
        return;
      }
      router.push(router.pathname);
      return;
    }
    if (shouldRedirect && !isInMaintenancePage) {
      router.replace('/maintenance-period');
    }
  }, [isPageMaintenance, maintenance, router.pathname]);

  return (
    <>
      {isPageMaintenance === undefined ? (
        <Loading />
      ) : (
        <>
          {children}
          <CreatorRoleModal
            isOpen={isOpenCreatorRoleModal}
            onCancel={() => {
              setIsOpenCreatorRoleModal(false);
            }}
          />
        </>
      )}
    </>
  );
}
