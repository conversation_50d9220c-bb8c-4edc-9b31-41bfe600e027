/* eslint-disable max-lines */
/* eslint-disable max-lines-per-function */
/* eslint-disable react/no-danger */
/* eslint-disable no-nested-ternary */

import CalendarIcon from '@/components/common/icons/CalendarIcon';
import { TypeCampaign, TypeCampaignReward, TypeTask } from '@/redux/endpoints/campaign';
import moment from 'moment';
import Image from 'next/image';
import React, { useEffect, useMemo, useState } from 'react';
import clsx from 'clsx';
import Notfound from '@/components/404';
import dayjs from 'dayjs';
import { parseMilliseconds } from '@/utils/func/convertCampaign';
import Link from 'next/link';
import CModalWapper from '@/components/common/CModalWapper';
import { useSelector } from 'react-redux';
import { RootState } from '@/redux/store';
import CButtonShadow from '@/components/common/CButtonShadow';
import { useRouter } from 'next/router';
import { TypeBannerResponse } from '@/redux/endpoints/banner';
import TemplateBanner from '@/components/Home/TemplateBanner';
import CampaignRewardSection from '../CampaignRewardSection';
import CampaignTasksSection from '../CampaignTasksSection';

export default function InfoCampaignContent({
  isCampaignExpired,
  campaignDetail,
  campaignCategory,
  campaignRewards,
  campaignTasks,
  isPreview,
  bannerUnderCampaign,
}: {
  isCampaignExpired: boolean;
  campaignDetail: TypeCampaign | null;
  campaignCategory: string;
  campaignRewards: TypeCampaignReward[] | null;
  campaignTasks: TypeTask[] | null;
  isPreview: boolean;
  bannerUnderCampaign: TypeBannerResponse['data'] | null;
}) {
  const { accessToken, user } = useSelector((state: RootState) => state.auth);

  const [isOpenModalReward, setIsOpenModalReward] = useState(false);
  const [countShowModal, setCountShowModal] = useState(0);
  const router = useRouter();

  const underCampaignBannerClass = clsx({
    'xl:[&_.custom-mission-campaign]:pb-[30px]': bannerUnderCampaign && bannerUnderCampaign?.length > 0,
  });

  const sortCampaignRewardPrice = useMemo(() => {
    const results = campaignRewards && Array.isArray(campaignRewards) ? [...campaignRewards] : [];

    return results?.sort((a, b) => a.amountOfMoney - b.amountOfMoney);
  }, [campaignRewards]);

  useEffect(() => {
    if (countShowModal === 0) {
      if (
        isPreview === false &&
        campaignDetail?.status === 'COMPLETION' &&
        campaignDetail?.methodOfselectWinners === 'AUTO_PRIZEE_DRAW' &&
        accessToken &&
        campaignDetail?.UserClaimCampaign &&
        Array.isArray(campaignDetail?.UserClaimCampaign) &&
        campaignDetail?.UserClaimCampaign?.length &&
        campaignDetail?.UserClaimCampaign?.[0]?.userId === user?.id &&
        campaignDetail?.coupon === null &&
        (campaignDetail?.UserClaimCampaign?.[0]?.award?.isWin === null ||
          campaignDetail?.UserClaimCampaign?.[0]?.award?.isWin === 'true')
      ) {
        setTimeout(() => {
          setIsOpenModalReward(true);
        }, 500);
        setCountShowModal(1);
      }
    }
  }, [isPreview, campaignDetail, accessToken, user, countShowModal]);

  if (isPreview === false && campaignDetail?.status !== 'PUBLIC' && campaignDetail?.status !== 'COMPLETION') {
    const now = dayjs();
    if (
      !campaignDetail?.startTime ||
      !dayjs(campaignDetail?.startTime).isValid() ||
      dayjs(campaignDetail?.startTime).diff(now) <= 0
    ) {
      return (
        <div>
          <Notfound
            customDescription={
              <div className="text-center font-bold text-[16px]">
                このキャンペーンは数日以内に開始する予定です。しばらくお待ちください。
              </div>
            }
            isShowBackBtn
          />
        </div>
      );
    }
    const { days, hours, minutes, seconds } = parseMilliseconds(dayjs(campaignDetail?.startTime).diff(now));
    return (
      <div>
        <Notfound
          customDescription={
            <div className="text-center font-bold text-[16px]">
              このキャンペーンは
              {`${
                days > 0
                  ? `${days}日`
                  : hours > 0
                    ? `${hours}時間`
                    : minutes > 0
                      ? `${minutes}分`
                      : seconds > 0
                        ? `${seconds}秒`
                        : '---'
              }`}
              後に開始します
            </div>
          }
          isShowBackBtn
        />
      </div>
    );
  }

  if (isPreview === false && campaignDetail?.status === 'COMPLETION') {
    return (
      <div>
        <Notfound
          customDescription={
            campaignDetail?.UserClaimCampaign?.[0]?.award?.isWin === null ||
            campaignDetail?.UserClaimCampaign?.[0]?.award?.isWin === 'true' ||
            campaignDetail?.UserClaimCampaign?.[0]?.award?.isWin === 'false' ? (
              <div className="text-center font-bold text-[16px]">
                本キャンペーンは終了しました
                <br />
                本キャンペーンで受け取ったギフトは
                <br />
                <Link className="text-[#04AFAF]" href="/my-page">
                  マイページ
                </Link>
                から再確認することができます。
              </div>
            ) : (
              <div className="text-center font-bold text-[16px]">
                本キャンペーンは終了しました
                <br />
                {campaignDetail?.endReason === 'WINNER_LIMIT_REACHED'
                  ? '本キャンペーンは当選者が全て出たため、応募することはできません。'
                  : '本キャンペーンは開催期間を終了したため、応募することはできません。'}
              </div>
            )
          }
          isShowBackBtn
        />
        <CModalWapper
          isOpen={isOpenModalReward}
          onCancel={() => {
            setIsOpenModalReward(false);
          }}
        >
          <div>
            <h3 className="text-[14px] font-bold tracking-[0.6px] leading-[30px] text-center  overflow-x-hidden text-ellipsis">
              本キャンペーンの報酬を受け取っていない状態です。当選画面へ移動しますか？
            </h3>
            <div className="h-[16px]" />
            <div className="w-[206px] h-[53px] mx-auto">
              <CButtonShadow
                onClick={() => {
                  router.replace(`/campaigns/${router?.query?.slug?.[0]}/winning`);
                }}
                title="はい"
                type="button"
              />
            </div>
          </div>
        </CModalWapper>
      </div>
    );
  }

  return (
    <div>
      <div className={clsx(isPreview ? '' : 'xl:flex xl:border-b-[2px] xl:border-b-[#333]')}>
        <div className={clsx(isPreview ? '' : 'xl:flex-1')}>
          {isPreview === false && isCampaignExpired === true && (
            <div
              className={clsx(
                'px-[20px] pt-[48px] !pb-0',
                isPreview ? '' : 'md:px-[160px] xl:px-[35px] xxl:px-[160px] md:py-[64px]'
              )}
            >
              <div className="rounded-[16px] border-[2px] border-[#333] px-[24px] py-[48px]">
                <p className="text-[20px] font-bold">本キャンペーンは終了しました</p>
                <div className="h-[16px]" />
                {campaignDetail?.UserClaimCampaign?.[0]?.award?.isWin ? (
                  <p className="text-[13px] ">
                    本キャンペーンで受け取ったギフトは
                    <Link className="text-[#04AFAF]" href="/my-page">
                      マイページ
                    </Link>
                    から再確認することができます。
                  </p>
                ) : campaignDetail?.endReason === 'WINNER_LIMIT_REACHED' ? (
                  <p className="text-[13px] ">本キャンペーンは当選者が全て出たため、応募することはできません。</p>
                ) : (
                  <p className="text-[13px] ">本キャンペーンは開催期間を終了したため、応募することはできません。</p>
                )}
              </div>
            </div>
          )}

          <div
            className={clsx(
              'bg-white px-[20px] pt-[48px] pb-[32px] md:pb-[48px]',
              isPreview ? '' : 'md:px-[160px] xl:px-[35px] xxl:px-[160px] md:!py-[48px]'
            )}
          >
            <div className="flex flex-col gap-[16px] ">
              <div className=" flex gap-[10px] items-center  ">
                <div className="w-[32px] min-w-[32px] h-[32px] md:w-[32px] md:h-[32px] rounded-full  overflow-hidden">
                  <Image
                    alt="company logo"
                    className="w-full h-full object-cover"
                    height="0"
                    sizes="100vw"
                    src={campaignDetail?.company?.image?.imageUrl ?? '/assets/images/ImagePlaceholder.png'}
                    width="0"
                  />
                </div>
                <p
                  className={clsx(
                    'font-bold text-[14px] tracking-[0.42px] leading-[21px] text-main-text ',
                    isPreview ? '' : 'md:text-[18px] md:tracking-[0.54px] md:leading-[27px]'
                  )}
                >
                  {campaignDetail?.company?.name ?? '-'}
                </p>
              </div>
              <div>
                <h3
                  className={clsx(
                    'font-bold text-[20px] tracking-[0.6px]  text-main-text ',
                    isPreview ? '' : 'md:text-[28px] md:tracking-[0.84px]'
                  )}
                >
                  {campaignDetail?.title ?? '-'}
                </h3>
                <div className={clsx('h-[8px]', isPreview ? '' : 'md:h-[16px]')} />
                {campaignCategory ? (
                  <span
                    className={clsx(
                      'inline-flex justify-center items-center rounded-full px-[12px] py-[3px]  border-gray-1 border-[1px]',
                      isPreview ? '' : 'md:px-[13px] md:py-[5px] '
                    )}
                  >
                    <span className="text-[12px]  tracking-[0.36px] leading-[18px]  text-gray-1">
                      {campaignCategory}
                    </span>
                  </span>
                ) : (
                  ''
                )}
              </div>
              <div
                className={clsx(
                  'flex items-center justify-center overflow-hidden',
                  isPreview ? '' : 'md:max-h-[680px] md:mt-[16px] md:mb-[18px] '
                )}
              >
                <Image
                  alt="campaign image"
                  className="w-full h-auto"
                  height={0}
                  sizes="100vw"
                  src={campaignDetail?.image?.imageUrl ?? '/assets/images/ImagePlaceholder.png'}
                  width={0}
                />
              </div>
              <div className={clsx(' flex flex-col gap-[6px] text-main-text', isPreview ? '' : ' md:gap-[16px] ')}>
                {campaignDetail?.methodOfselectWinners === 'MANUAL_SELECTION' && (
                  <p
                    className={clsx(
                      'flex gap-[12px] items-center text-[14px] tracking-[0.42px] ',
                      isPreview ? '' : 'md:text-[18px] md:tracking-[0.54px]'
                    )}
                  >
                    <Image
                      alt="campaign reward"
                      className="object-contain"
                      height={16}
                      src="/assets/images/reward.png"
                      width={16}
                    />
                    <p
                      className="text-[14px] font-bold break-all"
                      dangerouslySetInnerHTML={{ __html: campaignDetail?.noteReward?.replace(/\r?\n/g, '<br/>') ?? '' }}
                    />
                  </p>
                )}
                <p className="flex gap-[12px] items-center text-[14px] tracking-[0.42px] ">
                  <CalendarIcon className="w-[16px]" />
                  <span className="font-montserrat">
                    {moment(campaignDetail?.startTime)?.isValid()
                      ? moment(campaignDetail?.startTime)?.format('MM/DD HH:mm')
                      : '--/-- --:--'}
                    <span> 〜 </span>
                    {campaignDetail?.setExpiredTime === true &&
                      (moment(campaignDetail?.expiredTime)?.isValid()
                        ? moment(campaignDetail?.expiredTime)?.format('MM/DD HH:mm')
                        : '--/-- --:--')}
                  </span>
                </p>
                {campaignDetail?.methodOfselectWinners !== 'MANUAL_SELECTION' && (
                  <p
                    className={clsx(
                      'flex gap-[12px] items-center text-[14px] tracking-[0.42px] ',
                      isPreview ? '' : 'md:text-[18px] md:tracking-[0.54px]'
                    )}
                  >
                    <Image
                      alt="campaign reward"
                      className="object-contain"
                      height={16}
                      src="/assets/images/reward.png"
                      width={16}
                    />
                    <span className="font-bold">
                      {sortCampaignRewardPrice && sortCampaignRewardPrice?.length >= 2 ? (
                        <>
                          <span>
                            <span className="font-montserrat">
                              {sortCampaignRewardPrice?.[0]?.amountOfMoney &&
                              typeof sortCampaignRewardPrice?.[0]?.amountOfMoney === 'number'
                                ? sortCampaignRewardPrice[0].amountOfMoney.toLocaleString('ja-JP')
                                : '--'}
                            </span>
                            円
                          </span>
                          <span> 〜 </span>
                          <span>
                            <span className="font-montserrat">
                              {sortCampaignRewardPrice?.[sortCampaignRewardPrice.length - 1]?.amountOfMoney &&
                              typeof sortCampaignRewardPrice?.[sortCampaignRewardPrice.length - 1]?.amountOfMoney ===
                                'number'
                                ? sortCampaignRewardPrice[
                                    sortCampaignRewardPrice.length - 1
                                  ].amountOfMoney.toLocaleString('ja-JP')
                                : '--'}
                            </span>
                            円
                          </span>
                        </>
                      ) : sortCampaignRewardPrice?.length === 1 ? (
                        <span>
                          <span className="font-montserrat">
                            {sortCampaignRewardPrice[0]?.amountOfMoney.toLocaleString('ja-JP') ?? '--'}
                          </span>
                          円
                        </span>
                      ) : (
                        '--'
                      )}
                    </span>
                  </p>
                )}
              </div>
            </div>
          </div>
          <div
            className={clsx(
              " bg-[url('/assets/images/campaign_bg.png')] bg-no-repeat bg-cover bg-center rounded-[32px]  px-[20px] py-[32px] flex flex-col gap-[48px] ",
              isPreview ? '' : 'xl:rounded-[0px] md:px-[160px] xl:px-[35px] xxl:px-[160px] md:py-[48px] '
            )}
          >
            <div
              className="display-text-editer-content overflow-hidden text-ellipsis break-all"
              dangerouslySetInnerHTML={{ __html: campaignDetail?.description ?? '' }}
            />
            {campaignRewards && Array.isArray(campaignRewards) && campaignRewards?.length ? (
              <CampaignRewardSection
                campaignRewards={campaignRewards}
                isPreview={isPreview}
                typeCampaignWinner={campaignDetail?.methodOfselectWinners}
              />
            ) : (
              ''
            )}
          </div>
        </div>
        <div
          className={clsx(
            ' relative',
            isPreview ? '' : 'xl:w-[440px] xl:border-l-[2px] xl:border-l-[#333]',
            underCampaignBannerClass
          )}
        >
          <div className={isCampaignExpired === true ? 'opacity-40 pointer-events-none' : ''}>
            {campaignTasks && Array.isArray(campaignTasks) && campaignTasks?.length ? (
              <CampaignTasksSection
                campaignDetail={campaignDetail}
                campaignTasks={campaignTasks}
                isPreview={isPreview}
              />
            ) : (
              ''
            )}
          </div>
          {bannerUnderCampaign && bannerUnderCampaign?.length > 0 && (
            <div className="xl:pb-[30px] md:pb-[76px] pb-[32px]">
              <TemplateBanner banner={bannerUnderCampaign} device="both" location="under-campaign-mission" />
            </div>
          )}
        </div>
      </div>
      <div className={clsx(isPreview ? '' : 'xl:h-[24px]')} />
    </div>
  );
}
