/* eslint-disable react/no-danger */
/* eslint-disable no-nested-ternary */

import { useContext, useMemo } from 'react';
import { CampaignDetailContext } from '../CampainContext';
import InfoCampaignContent from './InfoCampaignContent';

export default function InfoCampaign() {
  const { campaignDetail, campaignRewards, campaignTasks, campaignCategory, bannerUnderCampaign } =
    useContext(CampaignDetailContext);

  const isCampaignExpired = useMemo(() => {
    let result = false;

    if (campaignDetail?.status === 'COMPLETION') {
      result = true;
    }
    return result;
  }, [campaignDetail]);

  return (
    <InfoCampaignContent
      bannerUnderCampaign={bannerUnderCampaign}
      campaignCategory={campaignCategory}
      campaignDetail={campaignDetail}
      campaignRewards={campaignRewards}
      campaignTasks={campaignTasks}
      isCampaignExpired={isCampaignExpired}
      isPreview={false}
    />
  );
}
