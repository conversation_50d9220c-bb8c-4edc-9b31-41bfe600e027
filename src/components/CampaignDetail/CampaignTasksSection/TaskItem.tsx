/* eslint-disable jsx-a11y/control-has-associated-label */
/* eslint-disable react/jsx-no-useless-fragment */
/* eslint-disable max-lines */
/* eslint-disable max-lines-per-function */
/* eslint-disable no-nested-ternary */
/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable no-console */
import CShadowCard from '@/components/common/CCardShadow';
import ArrowDown from '@/components/common/icons/ArrowDown';
import CopyIcon from '@/components/common/icons/Copy';
import { CopyToClipboard } from 'react-copy-to-clipboard';
import ArrowUpRightFormIcon from '@/components/common/icons/ArrowUpRightFormIcon';
import { TasksConvert } from '@/utils/func/convertCampaign';
import clsx from 'clsx';

import { useImplementTaskMutation } from '@/redux/endpoints/me';
import { RootState } from '@/redux/store';
import { getErrorMessage } from '@/utils/func/getErrorMessage';
import { openWindowPopup } from '@/utils/func/openWindowPopup';
import toastMessage from '@/utils/func/toastMessage';
import { Spin } from 'antd';
import Image from 'next/image';
import { useRouter } from 'next/router';
import { useContext, useState } from 'react';
import { useSelector } from 'react-redux';
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import { isFirefox, isMobile, isSafari } from 'react-device-detect';

import { REDIRECT_QUERY_KEY } from '@/utils/constant/enums';
import useConnectX from '@/hooks/useConnectX';
import ConnectXConfirmModal from '@/components/auth/ConnectXModal/ConnectXConfirmModal';
import { CampaignDetailContext } from '../CampainContext';
import ModalChooseMultiple from './ModalChooseMultiple';
import ModalChooseOne from './ModalChooseOne';
import ModalConnectX from './ModalConnectX';
import ModalFreeTextContent from './ModalFreeTextContent';

export default function TaskItem({
  task,
  isLoggedUserImplementedTask,
  type,
  isPreview,
}: {
  task: TasksConvert;
  isLoggedUserImplementedTask: boolean;
  type?: 'CONNECT_X' | 'CAMPAIGN_TASK';
  isPreview: boolean;
}) {
  const [onImplementTask] = useImplementTaskMutation();
  const router = useRouter();
  const { accessToken, user } = useSelector((state: RootState) => state.auth);

  const { isModalOpen, showModal, cancelModal, getTwitterOauthUrl } = useConnectX({
    handleAction: !accessToken || !user ? 'SIGNUP' : 'CONNECT',
    callBackPath: router.asPath,
  });

  const { onRefetchCampaignTasks, onFetchCampaignInfo } = useContext(CampaignDetailContext);

  const [isLoading, setIsLoading] = useState(false);
  const [modalState, setModalState] = useState<{
    isOpenModal: boolean;
    content: 'FAQ_FREE_TEXT' | 'FAQ_CHOOSE_ONE' | 'FAQ_CHOOSE_MULTIPLE' | 'CONNECT_X' | undefined;
  }>({
    isOpenModal: false,
    content: undefined,
  });

  const handleOpenPopup = (url: string) => {
    openWindowPopup(url, 'CLOUT', 1280, 768);
  };

  const handleCompleteMission = async () => {
    try {
      await onImplementTask({
        taskId: task?.id ?? '',
        body: {
          userShareId: router?.query?.userShareId ? Number(router?.query?.userShareId as string) : undefined,
        },
      }).unwrap();
    } catch (e) {
      toastMessage(getErrorMessage(e), 'error');
    } finally {
      await onRefetchCampaignTasks();
    }
  };

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const redirectUrlWindowReference = (
    taskLink: string | undefined,
    windowReference: Window | null,
    completedMission?: boolean
  ) => {
    if (taskLink) {
      if (windowReference) {
        if (isMobile) {
          if (completedMission) {
            handleCompleteMission();
          }
          windowReference?.location?.replace?.(taskLink);
        } else {
          windowReference.location = taskLink;
          if (completedMission) {
            setTimeout(async () => {
              handleCompleteMission();
            }, 5000);
          }
        }
      } else {
        handleOpenPopup(taskLink);
        if (completedMission) {
          setTimeout(async () => {
            handleCompleteMission();
          }, 5000);
        }
      }
    }
  };

  const onClickCard = async () => {
    try {
      if (isPreview) {
        const windowReference =
          task?.type === 'OPEN_LINK' && task?.link && (isMobile || isSafari || isFirefox)
            ? window.open('about:blank', '_blank')
            : null;

        switch (task?.type) {
          case 'OPEN_LINK': {
            redirectUrlWindowReference(task?.link, windowReference);
            break;
          }
          case 'FAQ_FREE_TEXT':
          case 'FAQ_CHOOSE_ONE':
          case 'FAQ_CHOOSE_MULTIPLE': {
            setModalState({
              isOpenModal: true,
              content: task?.type,
            });
            break;
          }
          default:
            break;
        }

        return;
      }
      setIsLoading(true);
      if (!accessToken || !user || (user && !user?.id)) {
        const ops = {
          [`${REDIRECT_QUERY_KEY}`]: router.asPath,
        };
        const qs = new URLSearchParams(ops).toString();
        router.push(`/auth/sign-in/campaign-implementer?${qs}`);
        return;
      }
      if (isLoggedUserImplementedTask && task.type !== 'SHARE_URL') {
        return;
      }

      if (user?.identities && user?.identities?.length > 0) {
        // fix safari bug
        const windowReference =
          task?.type === 'OPEN_LINK' && task?.link && (isMobile || isSafari || isFirefox)
            ? window.open('about:blank', '_blank')
            : null;

        const campaignInfo = await onFetchCampaignInfo?.();
        if (!campaignInfo || campaignInfo?.status !== 'PUBLIC') {
          windowReference?.close();
          router?.reload();
          return;
        }

        switch (task?.type) {
          case 'OPEN_LINK': {
            redirectUrlWindowReference(task?.link, windowReference, true);
            break;
          }
          case 'FAQ_FREE_TEXT':
          case 'FAQ_CHOOSE_ONE':
          case 'FAQ_CHOOSE_MULTIPLE': {
            setModalState({
              isOpenModal: true,
              content: task?.type,
            });
            break;
          }

          default:
            break;
        }
      } else {
        setModalState({
          isOpenModal: true,
          content: 'CONNECT_X',
        });
      }
    } catch (err: any) {
      toastMessage(getErrorMessage(err), 'error');
    } finally {
      setIsLoading(false);
    }
  };

  let contentRender = <div />;

  if (type === 'CONNECT_X') {
    contentRender = (
      <CShadowCard
        onClickCard={() => {
          showModal();
        }}
      >
        <div className="p-[24px] flex gap-[8px] flex-col">
          <div className={clsx('flex gap-[16px] items-center pb-[16px] border-b-[#AAA] border-b-[1px] ')}>
            <div className="w-[24px] h-[24px]">
              <Image
                alt="campaign image"
                className="w-full h-full object-cover"
                height="0"
                sizes="100vw"
                src="/assets/images/NotCheckIcon.png"
                width="0"
              />
            </div>
            <div className="flex-1">
              <div className="text-[16px] font-bold tracking-[0.48px] flex items-center gap-[4px] flex-wrap">
                <span>Xを連携する</span>

                <ArrowDown className=" rotate-[-90deg] w-[14px] h-[14px]" />
              </div>
            </div>
          </div>
          <div className="text-[13px]">参加するにはX連携が必要になります</div>
        </div>
      </CShadowCard>
    );
  } else {
    contentRender = (
      <>
        {task?.type === 'OPEN_LINK' ? (
          <CShadowCard onClickCard={onClickCard}>
            <div className="p-[24px] flex gap-[8px] flex-col">
              <div
                className={clsx(
                  'flex gap-[16px] items-center ',
                  task?.type === 'OPEN_LINK' ? 'pb-[16px] border-b-[#AAA] border-b-[1px] ' : ''
                )}
              >
                <div className="w-[24px] h-[24px]">
                  {isLoggedUserImplementedTask ? (
                    <Image
                      alt="campaign image"
                      className="w-full h-full object-cover"
                      height="0"
                      sizes="100vw"
                      src="/assets/images/CheckedIcon.png"
                      width="0"
                    />
                  ) : (
                    <Image
                      alt="campaign image"
                      className="w-full h-full object-cover"
                      height="0"
                      sizes="100vw"
                      src="/assets/images/NotCheckIcon.png"
                      width="0"
                    />
                  )}
                </div>
                <div className="flex-1">
                  {task?.required && (
                    <div className="text-white bg-[#04AFAF] px-[6px] py-[2px] w-fit max-w-10 text-[11px]">必須</div>
                  )}
                  <div className="text-[16px] font-bold tracking-[0.48px] flex items-center gap-[4px] flex-wrap">
                    <span>{task?.title ?? ''}</span>
                    <ArrowUpRightFormIcon />
                  </div>
                </div>
              </div>
              <div className="text-[14px]">{task?.description ?? ''}</div>
              <div className="text-[12px] text-right font-bold">
                {task?.points && task?.points !== 0 ? `${task?.points}ポイント` : ''}
              </div>
            </div>
          </CShadowCard>
        ) : (
          <CShadowCard onClickCard={onClickCard}>
            <div className="p-[24px] flex gap-[8px] flex-col">
              <div className={clsx('flex gap-[16px] items-center')}>
                {task?.type !== 'SHARE_URL' && (
                  <div className="w-[24px] h-[24px]">
                    {isLoggedUserImplementedTask ? (
                      <Image
                        alt="campaign image"
                        className="w-full h-full object-cover"
                        height="0"
                        sizes="100vw"
                        src="/assets/images/CheckedIcon.png"
                        width="0"
                      />
                    ) : (
                      <Image
                        alt="campaign image"
                        className="w-full h-full object-cover"
                        height="0"
                        sizes="100vw"
                        src="/assets/images/NotCheckIcon.png"
                        width="0"
                      />
                    )}
                  </div>
                )}
                <div className="flex-1">
                  {task?.required && (
                    <div className="text-white bg-[#04AFAF] px-[6px] py-[2px] w-fit max-w-10 text-[11px]">必須</div>
                  )}
                  <div className="text-[16px] font-bold tracking-[0.48px] flex items-center gap-[4px] flex-wrap">
                    <span>{task?.title ?? ''}</span>
                    {task?.type !== 'SHARE_URL' && <ArrowDown className=" rotate-[-90deg] w-[14px] h-[14px]" />}
                  </div>
                </div>
              </div>
              <div className="text-[14px]">{task?.description ?? ''}</div>
              {task?.type === 'SHARE_URL' && (
                <CopyToClipboard
                  onCopy={() => toastMessage('コピーしました！', 'success')}
                  text={`${window.location.origin}${window.location.pathname}?userShareId=${user?.id}`}
                >
                  <div className="flex space-x-3 border px-2 py-3">
                    <span
                      className="flex-1 w-[80%] line-clamp-2"
                      style={{ overflowWrap: 'break-word' }}
                    >{`${window.location.origin}${window.location.pathname}?userShareId=${user?.id}`}</span>
                    <CopyIcon />
                  </div>
                </CopyToClipboard>
              )}
              <div className="text-[12px] text-right font-bold">
                {task?.points && task?.points !== 0
                  ? `${task?.points}${task?.type === 'SHARE_URL' ? 'ポイント × 人数' : 'ポイント'}`
                  : ''}
              </div>
            </div>
          </CShadowCard>
        )}
      </>
    );
  }

  return (
    <Spin spinning={isLoading}>
      {contentRender}
      <ModalFreeTextContent
        isOpen={modalState?.isOpenModal && modalState?.content === 'FAQ_FREE_TEXT'}
        isPreview={isPreview}
        onCancel={() => {
          setModalState({
            isOpenModal: false,
            content: undefined,
          });
        }}
        task={task}
      />
      <ModalChooseOne
        isOpen={modalState?.isOpenModal && modalState?.content === 'FAQ_CHOOSE_ONE'}
        isPreview={isPreview}
        onCancel={() => {
          setModalState({
            isOpenModal: false,
            content: undefined,
          });
        }}
        task={task}
      />
      <ModalChooseMultiple
        isOpen={modalState?.isOpenModal && modalState?.content === 'FAQ_CHOOSE_MULTIPLE'}
        isPreview={isPreview}
        onCancel={() => {
          setModalState({
            isOpenModal: false,
            content: undefined,
          });
        }}
        task={task}
      />
      <ModalConnectX
        isOpen={modalState?.isOpenModal && modalState?.content === 'CONNECT_X'}
        onCancel={() => {
          setModalState({
            isOpenModal: false,
            content: undefined,
          });
        }}
      />
      <ConnectXConfirmModal
        isOpen={isModalOpen}
        onCancel={cancelModal}
        onConfirm={() => {
          cancelModal();
          getTwitterOauthUrl();
        }}
      />
    </Spin>
  );
}

TaskItem.defaultProps = {
  type: 'CAMPAIGN_TASK',
};
