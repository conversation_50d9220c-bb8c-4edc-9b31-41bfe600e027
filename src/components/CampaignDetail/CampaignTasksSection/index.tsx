/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable no-console */
/* eslint-disable max-lines-per-function */
/* eslint-disable max-lines */
import CButtonShadow from '@/components/common/CButtonShadow';
import CModalWapper from '@/components/common/CModalWapper';
import ArrowDown from '@/components/common/icons/ArrowDown';
import { useCreateGachaMutation } from '@/redux/endpoints/users';
import { RootState } from '@/redux/store';
import { convertCampaignTask, TasksConvert } from '@/utils/func/convertCampaign';
import { getErrorMessage } from '@/utils/func/getErrorMessage';
import toastMessage from '@/utils/func/toastMessage';
import { Spin } from 'antd';
import { useRouter } from 'next/router';
import { useContext, useMemo, useState } from 'react';
import { useSelector } from 'react-redux';

import clsx from 'clsx';
import { TypeCampaign, TypeTask } from '@/redux/endpoints/campaign';
import { REDIRECT_QUERY_KEY } from '@/utils/constant/enums';
import GoogleAd from '@/components/GoogleAd';
import { CampaignDetailContext } from '../CampainContext';
import TaskItem from './TaskItem';

export default function CampaignTasksSection({
  isPreview,
  campaignTasks,
  campaignDetail,
}: {
  isPreview: boolean;
  campaignTasks: TypeTask[] | null;
  campaignDetail: TypeCampaign | null;
}) {
  const { accessToken, user } = useSelector((state: RootState) => state.auth);
  const router = useRouter();
  const [isOpenModalSetupAuthEmail, setIsOpenModalSetupAuthEmail] = useState(false);
  const [onRegisterCampaign] = useCreateGachaMutation();
  const { isFetchingCampaignTasks, onFetchCampaignInfo } = useContext(CampaignDetailContext);

  const [isLoading, setIsLoading] = useState(false);

  const isDisableRegisterBtn = useMemo(() => {
    if (isPreview || (campaignDetail?.status !== 'PUBLIC' && campaignDetail?.status !== 'COMPLETION')) {
      return true;
    }
    if (!user?.id) {
      return true;
    }

    const hasTaskNotDone = campaignTasks?.some((item) => {
      if (!item?.taskTemplate?.required) return false;
      return (
        !Object.prototype.hasOwnProperty.call(item, 'UserTask') ||
        !item?.UserTask ||
        (item?.UserTask && !Array.isArray(item?.UserTask)) ||
        (item?.UserTask && Array.isArray(item?.UserTask) && !item?.UserTask?.length)
      );
    });

    if (user?.id && hasTaskNotDone === true) {
      return true;
    }
    return false;
  }, [user?.id, campaignDetail, campaignTasks]);

  const handleRegisterCampaign = async () => {
    try {
      if (isPreview || (campaignDetail?.status !== 'PUBLIC' && campaignDetail?.status !== 'COMPLETION')) {
        return;
      }
      setIsLoading(true);
      if (user?.id) {
        const infoCampaign = await onFetchCampaignInfo?.();

        if (!infoCampaign || infoCampaign?.status !== 'PUBLIC') {
          router?.reload();
          return;
        }

        const camapignId = router?.query?.slug?.[0] ?? '';
        if (campaignDetail?.methodOfselectWinners === 'MANUAL_SELECTION' && (!user?.emailId || !user?.havePassword)) {
          setIsOpenModalSetupAuthEmail(true);
          return;
        }
        if (
          campaignDetail?.methodOfselectWinners === 'AUTO_PRIZEE_DRAW' &&
          campaignDetail.UserClaimCampaign &&
          campaignDetail.UserClaimCampaign?.length > 0
        ) {
          const isWin = campaignDetail.UserClaimCampaign?.find((item) => item.award?.isWin === 'true');
          if (isWin) {
            router.replace(`/campaigns/${camapignId}/winning`);
            return;
          }
        }

        const gacha = await onRegisterCampaign({
          campaignId: camapignId,
          userId: user?.id ?? '',
        }).unwrap();

        if (onFetchCampaignInfo) {
          await onFetchCampaignInfo?.();
        }

        if (gacha?.isWin === true && campaignDetail?.methodOfselectWinners === 'MANUAL_SELECTION') {
          router.push(`/campaigns/${camapignId}/completion`);
          return;
        }
        if (gacha?.isWin === false && campaignDetail?.methodOfselectWinners === 'AUTO_PRIZEE_DRAW') {
          router.push(`/campaigns/${camapignId}/losing`);
          return;
        }
        if (gacha?.isWin === true && campaignDetail?.methodOfselectWinners === 'AUTO_PRIZEE_DRAW') {
          router.replace(`/campaigns/${camapignId}/winning`);
        }
      } else {
        router.push('/auth/sign-in/campaign-implementer');
      }
    } catch (e: any) {
      if (typeof e?.data?.message === 'string' && e?.data?.message?.includes('400008')) {
        if (campaignDetail?.methodOfselectWinners === 'MANUAL_SELECTION') {
          toastMessage(`${getErrorMessage(e)}、当選結果はキャンペーン制作者からの連絡をお待ちください `, 'error', true);
        } else {
          toastMessage(`${getErrorMessage(e)}、当選した場合マイページをご確認ください `, 'error', true);
        }
      } else {
        toastMessage(getErrorMessage(e), 'error', true);
      }
    } finally {
      setIsLoading(false);
    }
  };

  const isUserLogged = useMemo(() => {
    let result = true;
    if (
      !accessToken ||
      !user ||
      (user && !user?.id) ||
      !(user && user?.identities && Array.isArray(user?.identities) && user?.identities?.length > 0)
    ) {
      result = false;
    }
    if (isPreview === true) {
      result = true;
    }
    return result;
  }, [accessToken, user, isPreview]);

  return (
    <Spin spinning={isLoading || isFetchingCampaignTasks}>
      <div className="py-[56px] px-[20px] md:py-[100px] md:px-[160px] xl:px-[35px] custom-mission-campaign">
        <h3 className="text-[24px] font-bold tracking-[0.72px] text-center md:text-[28px] ">タスク</h3>
        <div className="h-[10px]" />

        <div className="text-center text-[11px] md:text-[13px] text-[#aaa]">
          タスク完了後、認証に時間がかかる場合があります
          <br />
          キャンペーンへ応募するには必須タスクを完了させてください
        </div>
        <div className="h-[24px]" />

        {isPreview === false && isUserLogged === false && campaignDetail?.status === 'PUBLIC' ? (
          <div className="mb-[8px]">
            <TaskItem
              isLoggedUserImplementedTask={false}
              isPreview={isPreview}
              task={{} as TasksConvert}
              type="CONNECT_X"
            />
          </div>
        ) : (
          ''
        )}
        <div
          className={clsx('flex flex-col gap-[8px]', isUserLogged === false ? 'opacity-40 pointer-events-none' : '')}
        >
          {campaignTasks &&
            Array.isArray(campaignTasks) &&
            campaignTasks?.length > 0 &&
            campaignTasks?.map((item) => {
              const result = convertCampaignTask(item);
              if (result) {
                return (
                  <TaskItem
                    isLoggedUserImplementedTask={Boolean(
                      user?.id && item?.UserTask && Array.isArray(item?.UserTask) && item?.UserTask?.length
                    )}
                    isPreview={
                      isPreview || (campaignDetail?.status !== 'PUBLIC' && campaignDetail?.status !== 'COMPLETION')
                    }
                    key={item?.id}
                    task={result}
                  />
                );
              }
              return '';
            })}
        </div>
        <div className="h-[40px] md:h-[64px]" />

        <div className=" flex items-center justify-center">
          <div className="w-[262px] h-[53px]">
            <CButtonShadow
              classBgColor={isDisableRegisterBtn ? 'bg-[#c2c2c2]' : 'bg-[#333]'}
              classBorderColor={isDisableRegisterBtn ? 'border-[#c2c2c2]' : 'border-[#333]'}
              classShadowColor="bg-[#fff]"
              isDisable={isDisableRegisterBtn}
              onClick={handleRegisterCampaign}
              textClass="text-white text-[14px] font-bold tracking-[0.42px]"
              title={
                campaignDetail?.methodOfselectWinners === 'MANUAL_SELECTION'
                  ? 'キャンペーンに応募する'
                  : '報酬を受け取る'
              }
              withIcon={{
                position: 'right',
                icon: <ArrowDown className="rotate-[-90deg]" />,
              }}
            />
          </div>
        </div>
        <div className="h-[40px] " />
        <ol className="text-gray-2 text-[13px] leading-[22px] tracking-[0.39px] list-disc list-inside">
          <li>キャンペーン期間中に応募、及び、当選賞品の受取を完了させてください。期限を過ぎると無効になります。</li>
          <li>
            当社は、このキャンペーンに関連する応募者や他の第三者に発生した損害について、一切責任を負いません。問題がある場合は、直接キャンペーン主催者にお問い合わせください。
          </li>
          <li>予告なくキャンペンの中止、終了、内容の変更がある場合がございます。</li>
          <li>商品の譲渡、転売、換金は固く禁止いたします。</li>
        </ol>
        <GoogleAd
          adClient="ca-pub-2602868131600259"
          // adFormat="auto"
          adSlot="8099594329"
          style={{ width: 336, height: 250, marginTop: 80 }}
        />
      </div>
      <CModalWapper
        isOpen={isOpenModalSetupAuthEmail}
        onCancel={() => {
          setIsOpenModalSetupAuthEmail(false);
        }}
      >
        <div>
          <h3 className="text-[24px] font-bold text-center">メール・パスワード登録</h3>
          <div className="h-[24px]" />
          <p>キャンペーンの応募にはメールアドレス・パスワードの登録が必要になります。</p>
          <div className="h-[24px]" />
          <div className="w-[206px] h-[53px] mx-auto">
            <CButtonShadow
              onClick={() => {
                if (accessToken) {
                  const ops = {
                    [`${REDIRECT_QUERY_KEY}`]: router.asPath,
                  };
                  const qs = new URLSearchParams(ops).toString();

                  if (user?.email?.email && user?.havePassword) {
                    return;
                  }
                  if (!user?.email?.email && !user?.havePassword) {
                    router.push(`/my-page/settings/auth-email?${qs}`);
                    return;
                  }
                  if (!user?.email?.email && user?.havePassword) {
                    router.push(`/my-page/settings/email?${qs}`);
                    return;
                  }
                  if (user?.email?.email && !user?.havePassword) {
                    router.push(`/my-page/settings/password?${qs}`);
                    return;
                  }
                  console.log('redirect');
                }
              }}
              title="設定"
              type="button"
            />
          </div>
        </div>
      </CModalWapper>
    </Spin>
  );
}
