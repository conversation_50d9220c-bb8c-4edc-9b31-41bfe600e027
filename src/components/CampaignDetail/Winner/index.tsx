/* eslint-disable no-alert */
/* eslint-disable import/no-extraneous-dependencies */
/* eslint-disable max-lines */
/* eslint-disable max-lines-per-function */
import CButtonClassic from '@/components/common/CButtonClassic';
import CModalWapper from '@/components/common/CModalWapper';
import { useCreateCouponMutation } from '@/redux/endpoints/coupons';
import { RootState } from '@/redux/store';
import { getErrorMessage } from '@/utils/func/getErrorMessage';
import toastMessage from '@/utils/func/toastMessage';
import Image from 'next/image';
import Link from 'next/link';
import { useContext, useEffect, useMemo, useState } from 'react';
import { useSelector } from 'react-redux';
import { Spin } from 'antd';
import { copyFunc } from '@/utils/copyFunc';
import AmazonDomainIcon from '@/components/common/icons/AmazonDomainIcon';
import { useRouter } from 'next/router';

import { CampaignDetailContext } from '../CampainContext';
import WinnerImage from './WinnerImage';

export default function Winner() {
  const { accessToken, user } = useSelector((state: RootState) => state.auth);
  const router = useRouter();
  const { campaignDetail, onFetchCampaignInfo } = useContext(CampaignDetailContext);

  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isRewardedCoupon, setIsRewardedCoupon] = useState(false);
  const [isWarningForNotRewardCoupon, setIsWarningForNotRewardCoupon] = useState(false);
  const [rewardToken, setRewardToken] = useState('');
  const [triggerCreateCoupon, { isLoading }] = useCreateCouponMutation();

  const baseCondition = useMemo(
    () =>
      accessToken &&
      campaignDetail?.UserClaimCampaign &&
      Array.isArray(campaignDetail?.UserClaimCampaign) &&
      campaignDetail?.UserClaimCampaign?.length &&
      campaignDetail?.UserClaimCampaign?.[0]?.userId === user?.id,
    [accessToken, campaignDetail?.UserClaimCampaign, user]
  );
  useEffect(() => {
    const handleRouteChange = () => {
      if (baseCondition && campaignDetail?.coupon === null && !isWarningForNotRewardCoupon) {
        window.alert(
          'まだ受け取りは完了していません。受け取り方法を選択して、ギフトを受け取ってください。選択せずに、キャンペーンが終了した場合、受け取りができなくなります。'
        );
        setIsWarningForNotRewardCoupon(true);
        router.events.emit('routeChangeError');
        // eslint-disable-next-line @typescript-eslint/no-throw-literal
        throw 'routeChange aborted.';
      }
    };

    router.events.on('routeChangeStart', handleRouteChange);

    return () => {
      router.events.off('routeChangeStart', handleRouteChange);
    };
  }, [baseCondition, campaignDetail?.coupon, router.events, isWarningForNotRewardCoupon]);

  const onCreateCoupon = async () => {
    try {
      if (baseCondition && (campaignDetail?.coupon !== null || isRewardedCoupon)) {
        toastMessage('すでに賞品を受取済みです。マイページにてご確認ください。', 'error');
      }
      if (baseCondition && campaignDetail?.coupon === null && !isRewardedCoupon) {
        const data = await triggerCreateCoupon({
          couponType: 'AMAZON_GIFT',
          campaignId: campaignDetail?.id,
        }).unwrap();
        if (data?.code) {
          setIsWarningForNotRewardCoupon(true);
          onFetchCampaignInfo?.();
          setRewardToken(data?.code);
          setIsModalOpen(true);
          setIsRewardedCoupon(true);
        }
      }
    } catch (error) {
      toastMessage(getErrorMessage(error), 'error');
    }
  };

  const handleCancel = () => {
    setIsModalOpen(false);
  };

  return (
    <div>
      <div className="bg-completion-task min-h-[450px] px-[20px] pt-[48px] pb-[56px] flex items-center justify-center flex-col gap-[24px] font-notoSans">
        <h1 className="font-montserrat text-[#F77803] text-[34px] font-bold leading-[41px]">Congratulations</h1>

        <WinnerImage />

        <h2 className="text-[#333] text-[28px] font-bold leading-[43px] text-center tracking-[0.84px] mb-[8px]">
          おめでとうございます！
          <br />
          {campaignDetail?.UserClaimCampaign?.[0]?.award?.campaignReward?.index ?? '---'}等{' '}
          {campaignDetail?.UserClaimCampaign?.[0]?.award?.campaignReward?.amountOfMoney.toLocaleString('ja-JP') ??
            '---'}
          円 に<br />
          当選されました
        </h2>
        <div className=" flex flex-col gap-[16px]">
          <div className="bg-white px-4 py-7 w-[335px] mx-auto text-center">
            <p className="font-bold text-[#F68083] text-[18px] mb-3">まだ受け取りは完了していません！</p>
            <p className=" text-[14px] text-[#333] font-medium ">
              以下ギフト券の画像をタップして賞品をお受け取りください。
            </p>
          </div>
          <Spin spinning={isLoading}>
            <div
              aria-hidden
              className="w-[335px] h-[200px] mx-auto rounded-[24px] bg-white p-[24px] flex flex-col cursor-pointer"
              onClick={onCreateCoupon}
            >
              <div className="flex items-center justify-between">
                <div className="flex gap-[4px] mt-[2px]">
                  <svg fill="none" height="11" viewBox="0 0 9 11" width="9" xmlns="http://www.w3.org/2000/svg">
                    <path
                      d="M3.18 10.7564V8.62641H0.75V7.33641H3.18V6.94641L2.82 6.20641H0.75V4.87641H2.2L0 0.316406H2.44L4.26 5.20641L6.19 0.316406H8.55L6.32 4.87641H7.75V6.16641H5.68L5.31 6.94641V7.29641H7.74V8.58641H5.32V10.7164L3.18 10.7564Z"
                      fill="#232F3E"
                    />
                  </svg>
                  <span className="font-bold text-[21px] relative top-[-3px] leading-[21px] font-montserrat max-w-[140px] overflow-hidden text-ellipsis">
                    {campaignDetail?.UserClaimCampaign?.[0]?.award?.campaignReward?.amountOfMoney.toLocaleString(
                      'ja-JP'
                    ) ?? '---'}
                  </span>
                </div>

                <AmazonDomainIcon />
              </div>
              <div className="flex-1 h-full flex items-center justify-center">
                <svg fill="none" height="33" viewBox="0 0 145 33" width="145" xmlns="http://www.w3.org/2000/svg">
                  <path
                    clipRule="evenodd"
                    d="M130.68 14.664C114.86 26.344 91.9102 32.554 72.1403 32.554C45.738 32.6966 20.2372 22.9586 0.650254 5.25396C-0.849746 3.93396 0.480251 2.11397 2.26025 3.13397C24.0411 15.5997 48.7044 22.1499 73.8003 22.134C92.5536 22.0405 111.103 18.2375 128.38 10.944C131.06 9.82396 133.31 12.734 130.68 14.664Z"
                    fill="#F7981D"
                    fillRule="evenodd"
                  />
                  <path
                    clipRule="evenodd"
                    d="M137.282 7.1335C135.282 4.5435 123.892 5.9235 118.792 6.5235C117.242 6.7035 117.002 5.3435 118.392 4.3835C127.462 -1.9765 142.322 -0.136497 144.022 1.9835C145.722 4.1035 143.582 18.9835 135.102 26.1135C133.782 27.2035 132.542 26.6435 133.102 25.1935C135.012 20.4035 139.302 9.7335 137.282 7.1335Z"
                    fill="#F7981D"
                    fillRule="evenodd"
                  />
                </svg>
              </div>
            </div>
          </Spin>
          <div className="text-[#777] text-[13px] leading-[22px]">
            <p>※一度受取方法を選択すると変更できません。ご了承ください。</p>
            <div className="h-[4px]" />
            <p>
              ※受け取った報酬は
              <Link className="font-bold" href="/my-page">
                マイページ
              </Link>
              から確認可能です。
            </p>
          </div>
        </div>
      </div>

      <CModalWapper isOpen={isModalOpen} onCancel={handleCancel}>
        <div>
          <div className="flex items-center justify-center ">
            <div className="hover:cursor-pointer w-[123px] h-[36px] ">
              <Image
                alt="amazon gift"
                className="w-full h-full object-contain"
                height={36}
                src="/assets/images/Amazon_Logo_RGB_SQUID.png"
                width={123}
              />
            </div>
          </div>
          <div className="h-[40px]" />
          <p className="text-[14px] font-medium">ギフトカード番号</p>
          <div className="h-[16px]" />
          <div className="bg-[#FFF4EA] px-[24px] py-[40px] rounded-[8px]">
            <p className="text-[#F77803] text-[20px] font-bold text-center">{rewardToken}</p>
            <div className="h-[16px]" />
            <div className="flex justify-end">
              <div className="w-[138px] h-[42px] ">
                <CButtonClassic
                  customClassName="!bg-white !text-[#333] !text-[12px] !border-white"
                  onClick={() => {
                    copyFunc(rewardToken);
                  }}
                  title="番号をコピー"
                  withIcon={{
                    position: 'left',
                    icon: (
                      <svg fill="none" height="12" viewBox="0 0 12 12" width="12" xmlns="http://www.w3.org/2000/svg">
                        <path
                          d="M8.5 10.5V11.4375C8.5 11.7656 8.24219 12 7.9375 12H1.5625C1.23438 12 1 11.7656 1 11.4375V2.8125C1 2.50781 1.23438 2.25 1.5625 2.25H3.25V9.1875C3.25 9.91406 3.83594 10.5 4.5625 10.5H8.5ZM8.5 2.4375C8.5 2.76562 8.73438 3 9.0625 3H11.5V9.1875C11.5 9.51562 11.2422 9.75 10.9375 9.75H4.5625C4.23438 9.75 4 9.51562 4 9.1875V0.5625C4 0.257812 4.23438 0 4.5625 0H8.5V2.4375ZM11.3125 1.71094C11.4297 1.82812 11.5 1.96875 11.5 2.10938V2.25H9.25V0H9.39062C9.53125 0 9.67188 0.0703125 9.78906 0.1875L11.3125 1.71094Z"
                          fill="#333333"
                        />
                      </svg>
                    ),
                  }}
                />
              </div>
            </div>
          </div>
          <div className="h-[16px]" />

          <div className="text-[#777] text-[13px] leading-[22px] tracking-[0.39px]">
            <p>※必ずギフトカード番号をお手元に控えてから画面を閉じてください。</p>
            <div className="h-[4px]" />
            <p>
              ※当選したギフト券は
              <Link className="font-bold" href="/my-page">
                マイページ
              </Link>
              から確認可能です。
            </p>
          </div>
        </div>
      </CModalWapper>
    </div>
  );
}
