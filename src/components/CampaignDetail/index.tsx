/* eslint-disable react/no-danger */
/* eslint-disable no-nested-ternary */

import { RootState } from '@/redux/store';
import { useContext, useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { useVisitCampaignMutation } from '@/redux/endpoints/me';
import { useRouter } from 'next/router';
import { CampaignDetailContext } from './CampainContext';
import Completion from './Completion';
import InfoCampaign from './InfoCampaign';
import Loser from './Loser';
import Winner from './Winner';

export default function CampaignDetail() {
  const { campaignDetail, viewType } = useContext(CampaignDetailContext);
  const router = useRouter();
  const [isVisited, setIsVisited] = useState<boolean>(false);
  const { accessToken, user } = useSelector((state: RootState) => state.auth);
  const [onVisitCampaign] = useVisitCampaignMutation();

  const visitCampaignWithLoginUser = async () => {
    try {
      await onVisitCampaign({
        campaignId: campaignDetail?.id ?? '',
        userShareId: router?.query?.userShareId ? Number(router?.query?.userShareId as string) : undefined,
      }).unwrap();
      setIsVisited(true);
    } catch (e) {
      // eslint-disable-next-line no-console
      console.log('error visitCampaignWithLoginUser', e);
    }
  };

  useEffect(() => {
    if (user && campaignDetail?.id && !isVisited) {
      visitCampaignWithLoginUser();
    }
  }, [user, router.pathname, campaignDetail]);

  const baseCondition = Boolean(
    accessToken &&
      (campaignDetail?.status === 'PUBLIC' || campaignDetail?.status === 'COMPLETION') &&
      campaignDetail?.UserClaimCampaign &&
      Array.isArray(campaignDetail?.UserClaimCampaign) &&
      campaignDetail?.UserClaimCampaign?.length
  );

  if (viewType === 'detail') {
    return <InfoCampaign />;
  }
  if (viewType === 'losing' && baseCondition && campaignDetail?.UserClaimCampaign?.[0]?.award?.isWin === 'false') {
    return <Loser />;
  }
  if (viewType === 'completion' && baseCondition && campaignDetail?.UserClaimCampaign?.[0]?.award?.isWin === 'true') {
    return <Completion />;
  }
  if (
    viewType === 'winning' &&
    baseCondition &&
    campaignDetail?.UserClaimCampaign?.[0]?.userId === user?.id &&
    (campaignDetail?.UserClaimCampaign?.[0]?.award?.isWin === null ||
      campaignDetail?.UserClaimCampaign?.[0]?.award?.isWin === 'true')
  ) {
    return <Winner />;
  }
  return <div />;
}
