import { DataMaintenanceItem } from '@/redux/endpoints/maintenance';
import { Image } from 'antd';
import dayjs from 'dayjs';

export default function Maintenance({ data }: { data: DataMaintenanceItem }) {
  return (
    <div className="flex flex-1 bg-[#F0F7F6] px-4 justify-center items-center h-full container-min-height py-6">
      <div className="flex flex-col items-center">
        <Image
          alt="maintenance-banner"
          className="cursor-pointer"
          height={224}
          preview={false}
          src="/assets/images/maintenance_banner.png"
          width={287}
        />
        <p className="text-[18px] xs:text-[24px] text-center font-bold my-6">
          現在、サイトのメンテナンスを実施しております
        </p>
        <p className="text-[12px] xs:text-[14px] text-center">
          メンテナンス終了は{dayjs(data.endAt).format('YYYY/MM/DD HH:mm')} 頃を予定しております。
        </p>
      </div>
    </div>
  );
}
