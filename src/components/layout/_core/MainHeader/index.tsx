/* eslint-disable max-lines */
/* eslint-disable max-lines-per-function */
import LogoutModal from '@/components/auth/LogoutModal';
import CButtonShadow from '@/components/common/CButtonShadow';
import ArrowDown from '@/components/common/icons/ArrowDown';
import BarIcon from '@/components/common/icons/BarIcon';
import XIcon from '@/components/common/icons/XIcon';
import CreatorRoleModal from '@/components/CreatorRoleFeedbackModal/CreatorRoleModal';
import { DataMaintenanceItem, useGetMaintenanceQuery } from '@/redux/endpoints/maintenance';
import { setIsOpenMainMenu } from '@/redux/slices/common.slice';
import { RootState } from '@/redux/store';
import clsx from 'clsx';
import crypto from 'crypto';
import dayjs from 'dayjs';
import Image from 'next/image';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { useEffect, useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

interface MainNavigationType {
  key: number;
  text: string;
  to: string;
  isRedirectByAction?: boolean;
  target?: string;
}

type PopupNoneAuthenticateData = {
  href: string;
  text: string;
  textOk: string;
};

const dataNoneAuthenticate = {
  email: {
    href: '/my-page/settings/auth-email',
    text: 'メールアドレスを認証して下さい',
    textOk: 'メールアドレス登録',
  },

  phone: {
    href: '/my-page/settings/two-step-authentication/configure-phone-number',
    text: 'SMS認証を行ってください。',
    textOk: '認証する',
  },
};

export default function MainHeader() {
  const [isOpenLogoutModal, setIsOpenLogoutModal] = useState(false);
  const { data: campaignDetailTasks } = useGetMaintenanceQuery({});
  const [maintenance, setMaintenance] = useState<DataMaintenanceItem | undefined>();
  const { accessToken, user } = useSelector((store: RootState) => store.auth);
  const { isOpenMainMenu } = useSelector((store: RootState) => store.common);
  const [isOpenCreatorRoleModal, setIsOpenCreatorRoleModal] = useState(false);
  const [dataPopupUnAuthenticate, setDataPopupUnAuthenticate] = useState<PopupNoneAuthenticateData>(
    dataNoneAuthenticate.phone
  );
  const router = useRouter();
  const dispatch = useDispatch();

  const MainNavigation: MainNavigationType[] = useMemo(() => {
    const links: MainNavigationType[] = [
      { key: 1, text: 'Home', to: '/' },
      { key: 3, text: '抽選でもらう', to: '/campaigns' },
      { key: 4, text: 'マイページ', to: '/my-page' },
      { key: 5, text: 'キャンペーン作成', to: '/campaign-creator' },
      { key: 6, text: 'お問い合わせ', to: '/inquiry' },
      { key: 7, text: 'よくある質問', target: '_blank', to: 'https://medium.com/@clout-fi/faq-1fb0e283f5f8' },
    ];
    let link = '/auth/sign-in/campaign-implementer';
    let isRedirectByAction = false;

    if (user) {
      const userId = user.id;
      const digestString = `${process.env.NEXT_PUBLIC_APPFROM};${process.env.NEXT_PUBLIC_GIFT_TYPE};${userId};${process.env.NEXT_PUBLIC_MEDIA_ID}${process.env.NEXT_PUBLIC_SITE_KEY}`;
      const digest = crypto.createHash('sha256').update(digestString).digest('hex');
      if (user?.twoFactorMethod === 'NONE' || user?.emailId === null) {
        isRedirectByAction = true;
      }

      link = `https://sandbox.appdriver.jp/5/v1/index/${process.env.NEXT_PUBLIC_SITE_ID}?identifier=${userId}&media_id=${process.env.NEXT_PUBLIC_MEDIA_ID}&appfrom=${process.env.NEXT_PUBLIC_APPFROM}&gift_type=${process.env.NEXT_PUBLIC_GIFT_TYPE}&digest=${digest}`;

      if (process.env.NEXT_PUBLIC_IS_PRODUCTION === '1') {
        link = `https://appdriver.jp/5/v1/index/${process.env.NEXT_PUBLIC_SITE_ID}?identifier=${userId}&media_id=${process.env.NEXT_PUBLIC_MEDIA_ID}&appfrom=${process.env.NEXT_PUBLIC_APPFROM}&gift_type=${process.env.NEXT_PUBLIC_GIFT_TYPE}&digest=${digest}`;
      }
    }
    links.splice(1, 0, {
      key: 2,
      text: '確実にもらう',
      to: link,
      target: '_blank',
      isRedirectByAction,
    });
    return links;
  }, [user]);

  const SubNavigation = useMemo(
    () => [
      {
        key: 1,
        text: '利用規約',
        to: router.pathname.startsWith('/auth/sign-in/campaign-implementer')
          ? '/terms-of-service?view=implementer'
          : '/terms-of-service',
      },
      {
        key: 2,
        text: '特定商取引法に基づく表示',
        to: '/specified-commercial-transactions-law',
      },
      {
        key: 3,
        text: 'プライバシーポリシー',
        to: '/privacy-policy',
      },
    ],
    []
  );

  useEffect(() => {
    if (campaignDetailTasks?.data?.data) {
      const activeMaintenance = campaignDetailTasks?.data?.data?.find((item) => item);
      setMaintenance(activeMaintenance);
    }
  }, [campaignDetailTasks]);

  const isPageMaintenance = useMemo(() => {
    if (maintenance) {
      if (dayjs(maintenance.endAt).unix() > dayjs().unix() && dayjs(maintenance.startAt).unix() <= dayjs().unix()) {
        return true;
      }
      return false;
    }
    return undefined;
  }, [maintenance]);

  const onChangeAuth = async () => {
    try {
      dispatch(setIsOpenMainMenu(false));
      if (accessToken) {
        setIsOpenLogoutModal(true);
        return;
      }
      router.push('/auth/sign-in/campaign-implementer');
    } catch (err) {
      // eslint-disable-next-line no-console
      console.log(err);
    }
  };

  useEffect(() => {
    if (isOpenMainMenu) {
      document.body.classList.add('stop-scrolling');
    } else {
      document.body.classList.remove('stop-scrolling');
    }
  }, [isOpenMainMenu]);

  useEffect(() => {
    const start = () => {
      dispatch(setIsOpenMainMenu(false));
    };
    const end = () => {};
    router.events.on('routeChangeStart', start);
    router.events.on('routeChangeComplete', end);
    router.events.on('routeChangeError', end);

    return () => {
      router.events.off('routeChangeStart', start);
      router.events.off('routeChangeComplete', end);
      router.events.off('routeChangeError', end);
    };
  }, []);

  useEffect(() => {
    if (!user) return;

    if (user.twoFactorMethod === 'NONE') {
      setDataPopupUnAuthenticate(dataNoneAuthenticate.phone);
    } else if (user.emailId === null) {
      setDataPopupUnAuthenticate(dataNoneAuthenticate.email);
    }
  }, [user]);

  return (
    <div className="font-notoSans  bg-white ">
      <CreatorRoleModal
        isOpen={isOpenCreatorRoleModal}
        onCancel={() => setIsOpenCreatorRoleModal(false)}
        onOk={() => router.push(dataPopupUnAuthenticate?.href)}
        text={dataPopupUnAuthenticate?.text}
        textCancel="キャンセル"
        textOk={dataPopupUnAuthenticate?.textOk}
      />
      <div className="h-[var(--main-header-height-mobile)] xl:h-[var(--main-header-height-pc)] px-[5px] xs:px-[20px] xl:px-[48px] flex justify-between items-center w-full  border-t-[2px] border-b-[2px] border-[#333] border-solid  ">
        <Link className="w-[81px] h-[24px] xl:w-[100px] xl:h-[30px] hover:cursor-pointer" href="/">
          <Image
            alt="logo"
            className="w-full h-full object-cover"
            height="0"
            sizes="100vw"
            src="/assets/images/logo 1.png"
            width="0"
          />
        </Link>
        {!isPageMaintenance && (
          <>
            <div className="hidden xl:flex items-center justify-end gap-[48px]">
              <div className="flex items-center justify-end gap-[30px]">
                {MainNavigation.slice(1, 6).map((item) => {
                  if (item.isRedirectByAction) {
                    return (
                      // eslint-disable-next-line jsx-a11y/click-events-have-key-events, jsx-a11y/no-noninteractive-element-interactions
                      <p
                        className={clsx('text-[14px] font-bold tracking-[0.42px] leading-[21px] cursor-pointer')}
                        key={item.key}
                        onClick={() => {
                          setIsOpenCreatorRoleModal(true);
                        }}
                      >
                        {item.text}
                      </p>
                    );
                  }
                  return (
                    <Link
                      className={clsx('text-[14px] font-bold tracking-[0.42px] leading-[21px] ')}
                      href={item.to}
                      key={item.key}
                      target={item.target === '_blank' ? '_blank' : '_self'}
                    >
                      {item.text}
                    </Link>
                  );
                })}
              </div>
              <div className="flex items-center justify-end gap-[24px] ">
                {accessToken ? (
                  ''
                ) : (
                  <Link
                    className="text-[13px] font-bold pb-[6px] border-b-[2px] border-b-[#333] flex items-center justify-center gap-[4px] !w-fit mx-auto mt-[6px] "
                    href="/auth/sign-up"
                  >
                    新規会員登録の方はこちら <ArrowDown className=" rotate-[-90deg] w-[14px] h-[14px]" />
                  </Link>
                )}
                <div className="w-[206px] h-[53px]">
                  <CButtonShadow
                    onClick={onChangeAuth}
                    textClass="text-white !text-[14px] font-notoSans"
                    title={accessToken ? 'ログアウト ' : 'ログイン '}
                  />
                </div>
              </div>
            </div>
            <div className="w-[42px] h-[42px] xl:hidden">
              <CButtonShadow
                classBgColor="bg-white"
                classRounded="rounded-[4px]"
                classShadowColor="bg-main-text"
                onClick={() => {
                  dispatch(setIsOpenMainMenu(true));
                }}
                shadowSize="small"
                withIcon={{
                  position: 'right',
                  icon: <BarIcon />,
                }}
              />
            </div>
          </>
        )}
      </div>
      <div
        aria-hidden="true"
        className={clsx(
          'fixed top-0 w-full h-screen z-[1000] duration-500 transition-all xl:hidden',
          isOpenMainMenu ? 'right-0' : 'right-[-100vw]'
        )}
        onClick={() => {
          dispatch(setIsOpenMainMenu(false));
        }}
      >
        <div
          className={clsx(
            'fixed top-0 left-0 w-full h-screen  bg-[#333]/[60%] duration-200 transition-all ',
            isOpenMainMenu ? 'opacity-100 visible z-[1000]' : 'invisible opacity-0 z-[-1]'
          )}
        />

        <div
          aria-hidden="true"
          className={clsx(
            ' bg-white w-[302px] h-[75vh] border-[2px] border-[#333] border-r-[0px] absolute z-[1001]  duration-500 transition-all  py-[88px] pb-[10px] px-[48px] pr-[20px]',
            isOpenMainMenu ? ' right-0 top-0' : ' right-[-500px] top-0'
          )}
          onClick={(e) => e.stopPropagation()}
        >
          <div className="w-full h-full overflow-y-auto pb-[70px] pr-[28px]">
            <div className="w-[42px] h-[42px] absolute right-[20px] top-[11px] z-[1111] ">
              <CButtonShadow
                classBgColor="bg-white"
                classRounded="rounded-[4px]"
                classShadowColor="bg-main-text"
                onClick={() => {
                  dispatch(setIsOpenMainMenu(false));
                }}
                shadowSize="small"
                withIcon={{
                  position: 'right',
                  icon: <XIcon />,
                }}
              />
            </div>
            <div className="flex flex-col gap-[24px]">
              {MainNavigation.map((i) =>
                i.isRedirectByAction ? (
                  // eslint-disable-next-line jsx-a11y/click-events-have-key-events, jsx-a11y/no-noninteractive-element-interactions
                  <p
                    className={clsx('text-[20px] font-bold tracking-[0.6px]  ')}
                    key={i.key}
                    onClick={() => {
                      setIsOpenCreatorRoleModal(true);
                    }}
                  >
                    {i.text}
                  </p>
                ) : (
                  <Link
                    className={clsx(
                      'text-[20px] font-bold tracking-[0.6px]  ',
                      i.text === 'Home' ? 'font-montserrat' : '',
                      router.pathname === i.to || (router.pathname.startsWith(i.to) && i.to !== '/')
                        ? ' text-[#04AFAF]'
                        : ''
                    )}
                    href={i.to}
                    key={i.key}
                    target={i.target === '_blank' ? '_blank' : '_self'}
                  >
                    {i.text}
                  </Link>
                )
              )}
            </div>
            <div className="h-[40px]" />
            <div className=" h-[53px]">
              <CButtonShadow
                onClick={onChangeAuth}
                textClass="text-white !text-[14px] font-notoSans tracking-[0.42px]"
                title={accessToken ? 'ログアウト' : 'ログイン'}
              />
            </div>
            {accessToken ? (
              ''
            ) : (
              <Link
                className="text-[13px] font-bold pb-[6px] border-b-[2px] border-b-[#333] flex items-center justify-center !w-fit mx-auto mt-[16px] gap-[4px]"
                href="/auth/sign-up"
              >
                新規会員登録の方はこちら <ArrowDown className=" rotate-[-90deg] w-[14px] h-[14px]" />
              </Link>
            )}
            <div className="h-[40px]" />
            <div className="flex flex-col gap-[16px]">
              {SubNavigation.map((i) => (
                <Link
                  className={clsx(
                    'text-[13px]  tracking-[0.4px]  ',
                    router.pathname.startsWith(i.to) ? ' text-[#04AFAF]' : ''
                  )}
                  href={i.to}
                  key={i.key}
                >
                  {i.text}
                </Link>
              ))}
            </div>
          </div>
        </div>
      </div>
      <LogoutModal
        isOpen={isOpenLogoutModal}
        onCancel={() => {
          setIsOpenLogoutModal(false);
        }}
      />
    </div>
  );
}
