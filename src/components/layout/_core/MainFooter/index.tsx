import LineIcon from '@/components/common/icons/LineIcon';
import { DataMaintenanceItem, useGetMaintenanceQuery } from '@/redux/endpoints/maintenance';
import clsx from 'clsx';
import dayjs from 'dayjs';
import Image from 'next/image';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { isMobile } from 'react-device-detect';
import { useEffect, useMemo, useState } from 'react';

export default function MainFooter() {
  const { data: campaignDetailTasks, isFetching } = useGetMaintenanceQuery({});
  const [maintenance, setMaintenance] = useState<DataMaintenanceItem | undefined>();
  useEffect(() => {
    if (campaignDetailTasks?.data?.data) {
      const activeMaintenance = campaignDetailTasks?.data?.data?.find((item) => item);
      if (activeMaintenance) {
        setMaintenance(activeMaintenance);
      } else {
        setMaintenance(undefined);
      }
    }
  }, [campaignDetailTasks]);

  const isPageMaintenance = useMemo(() => {
    if (isFetching) {
      return undefined;
    }
    if (
      maintenance &&
      dayjs(maintenance.endAt).unix() > dayjs().unix() &&
      dayjs(maintenance.startAt).unix() <= dayjs().unix()
    ) {
      return true;
    }
    return false;
  }, [maintenance, isFetching]);

  const router = useRouter();
  const FooterNavigation = [
    isPageMaintenance
      ? undefined
      : {
          key: 1,
          text: 'Home',
          to: '/',
        },
    isPageMaintenance ? undefined : { key: 2, text: 'キャンペーン', to: '/campaigns' },
    isPageMaintenance
      ? undefined
      : {
          key: 3,
          text: 'マイページ',
          to: '/my-page',
        },
    isPageMaintenance
      ? undefined
      : {
          key: 4,
          text: 'キャンペーン作成',
          to: '/campaign-creator/create',
        },
    {
      key: 5,
      text: 'お問い合わせ',
      to: '/inquiry',
    },
    {
      key: 6,
      text: 'よくある質問',
      to: 'https://medium.com/@clout-fi/faq-1fb0e283f5f8',
    },
    {
      key: 7,
      text: '利用規約',
      to: router.pathname.startsWith('/auth/sign-in/campaign-implementer')
        ? '/terms-of-service?view=implementer'
        : '/terms-of-service',
    },
    {
      key: 8,
      text: '特定商取引法に基づく表示',
      to: '/specified-commercial-transactions-law',
    },
    {
      key: 9,
      text: 'プライバシーポリシー',
      to: '/privacy-policy',
    },
  ];
  const FooterSocialLinksV1 = [
    {
      key: 1,
      to: 'https://x.com/clout_fi',
      icon: <Image alt="x icon" height={40} src="/assets/images/footer-x-img.png" width={40} />,
    },
    {
      key: 2,
      to: isMobile ? 'https://lin.ee/FG4N9NR' : 'https://line.me/ti/g2/E1dyhWgzM-qVKuF2h2UQwlpt5q6lpkluAQfQlw',
      icon: <LineIcon />,
    },
  ];
  return (
    <div className="flex items-center flex-col gap-[40px] lg:gap-[100px] pt-[56px] pb-[16px] lg:pb-[24px] lg:pt-[100px] font-notoSans">
      <div className="flex items-center flex-col gap-[40px] lg:flex-row lg:justify-between lg:items-end  max-w-[1000px] lg:w-[1000px] mx-auto">
        <div className="flex items-center flex-col gap-[40px] lg:gap-[32px] lg:items-start ">
          <div
            aria-hidden
            className="w-[81px] h-[24px] lg:w-[135px] lg:h-[40px]"
            onClick={() => {
              if (!isPageMaintenance) {
                router.push('/');
              }
            }}
          >
            <Image
              alt="footer logo"
              className="w-full h-full object-cover"
              height="0"
              sizes="100vw"
              src="/assets/images/logo 1.png"
              width="0"
            />
          </div>

          <div className="flex flex-col gap-[16px] items-center justify-center lg:items-start">
            <div className="flex flex-col lg:flex-row gap-[16px] items-center  justify-center">
              <div className="flex flex-wrap gap-[16px] items-center justify-center">
                {FooterNavigation.slice(0, 3).map(
                  (i) =>
                    i && (
                      <Link
                        className={clsx('text-[16px] font-medium', i.text === 'Home' ? 'font-montserrat' : '')}
                        href={i.to}
                        key={i.key}
                      >
                        {i.text}
                      </Link>
                    )
                )}
              </div>
              <div className="flex flex-wrap gap-[16px] items-center justify-center">
                {FooterNavigation.slice(3, 6).map(
                  (i) =>
                    i && (
                      <Link
                        className="text-[16px] font-medium"
                        href={i.to}
                        key={i.key}
                        target={i.text === 'よくある質問' ? '_blank' : '_self'}
                      >
                        {i.text}
                      </Link>
                    )
                )}
              </div>
            </div>
            <div className="flex flex-wrap gap-[16px] lg:gap-[0px] items-center justify-center">
              {FooterNavigation.slice(6, 9).map(
                (i, idx) =>
                  i && (
                    <Link
                      className={clsx(
                        'text-[12px] font-medium lg:px-[24px] lg:border-r-[1px] lg:border-r-[#aaa] lg:text-[11px] leading-[16px]',
                        idx === 0 ? 'lg:border-l-[1px] lg:border-l-[#aaa] ' : ' '
                      )}
                      href={i.to}
                      key={i.key}
                    >
                      {i.text}
                    </Link>
                  )
              )}
            </div>
          </div>
        </div>

        <div className="flex flex-wrap gap-[16px]">
          {FooterSocialLinksV1.map(
            (i) =>
              i && (
                <Link href={i.to} key={i.key} target="_blank">
                  {i.icon}
                </Link>
              )
          )}
        </div>
      </div>

      <div className=" w-full border-t-solid border-t-[2px] border-t-[#333] pt-[14px] lg:pt-[22px] text-[13px] text-center text-gray-2 font-[350] leading-[19px]">
        Copyright © 2024 clout All Rights Reserved
      </div>
    </div>
  );
}
