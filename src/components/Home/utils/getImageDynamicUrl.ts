import { User } from '@/types/auth.type';
import crypto from 'crypto';

export const getImageDynamicUrl = (user: User | null) => {
  let link = '';
  if (user) {
    const userId = user.id;
    const digestString = `${process.env.NEXT_PUBLIC_APPFROM};${process.env.NEXT_PUBLIC_GIFT_TYPE};${userId};${process.env.NEXT_PUBLIC_MEDIA_ID}${process.env.NEXT_PUBLIC_SITE_KEY}`;
    const digest = crypto.createHash('sha256').update(digestString).digest('hex');

    link = `https://sandbox.appdriver.jp/5/v1/index/${process.env.NEXT_PUBLIC_SITE_ID}?identifier=${userId}&media_id=${process.env.NEXT_PUBLIC_MEDIA_ID}&appfrom=${process.env.NEXT_PUBLIC_APPFROM}&gift_type=${process.env.NEXT_PUBLIC_GIFT_TYPE}&digest=${digest}`;

    if (process.env.NEXT_PUBLIC_IS_PRODUCTION === '1') {
      link = `https://appdriver.jp/5/v1/index/${process.env.NEXT_PUBLIC_SITE_ID}?identifier=${userId}&media_id=${process.env.NEXT_PUBLIC_MEDIA_ID}&appfrom=${process.env.NEXT_PUBLIC_APPFROM}&gift_type=${process.env.NEXT_PUBLIC_GIFT_TYPE}&digest=${digest}`;
    }
  }

  return link;
};
