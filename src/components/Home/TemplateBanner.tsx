import React, { useMemo } from 'react';
import { BannerData, TypeBannerResponse } from '@/redux/endpoints/banner';
import { Carousel } from 'antd';
import Image from 'next/image';
import { useSelector } from 'react-redux';
import { RootState } from '@/redux/store';
import styles from './index.module.scss';
import { getImageDynamicUrl } from './utils/getImageDynamicUrl';

interface Props {
  banner: TypeBannerResponse['data'] | null;
  device: 'desktop' | 'mobile' | 'both';
  location: 'top' | 'footer' | 'under-campaign-mission';
}

interface PropsComponentBanner {
  device: 'desktop' | 'mobile' | 'both';
  location: 'top' | 'footer' | 'under-campaign-mission';
  bannerItem: BannerData | undefined;
}

const contentStyle = (device, location) => {
  if (location === 'top' && device === 'desktop') {
    return {
      height: 250,
      width: 970,
      lineHeight: 250,
    };
  }
  if (location === 'footer' && device === 'desktop') {
    return {
      height: 90,
      width: 970,
      lineHeight: 90,
    };
  }
  if (
    ((location === 'top' || location === 'footer') && device === 'mobile') ||
    (device === 'both' && location === 'under-campaign-mission')
  ) {
    return {
      height: 250,
      width: 336,
      lineHeight: 250,
    };
  }
  if (location === 'under-campaign-mission') {
    return {
      height: 250,
      width: 336,
      lineHeight: 250,
    };
  }
  return {};
};

const CarouselBanner = ({ device, location, bannerItem }: PropsComponentBanner) => {
  const dataSize = useMemo(() => contentStyle(device, location), [device, location]);
  const carouselItems = useMemo(() => bannerItem?.images || [], [bannerItem]);
  const { user } = useSelector((store: RootState) => store.auth);

  return (
    <div className="!mx-[-20px]">
      {carouselItems && carouselItems?.length > 0 ? (
        <div className={`${carouselItems?.length > 1 ? 'pb-[20px]' : ''}  ${styles.customCarousel}`}>
          <Carousel autoplay autoplaySpeed={4000} infinite>
            {carouselItems?.map((item) => {
              const dynamicUrl = getImageDynamicUrl(user);
              const url = dynamicUrl && item.dynamic_url ? dynamicUrl : item.url;

              return (
                <div key={item.id}>
                  <div className="flex justify-center m-[0_auto]">
                    <div
                      style={{
                        position: 'relative',
                        width: `${dataSize.width}px`,
                        height: `${dataSize.height}px`,
                      }}
                    >
                      <Image
                        alt={item.alt_image ?? ''}
                        className={`z-[5] ${url ? 'cursor-pointer' : ''}`}
                        fill
                        onClick={() => {
                          if (url) {
                            window.open(url ?? '', '_blank');
                          }
                        }}
                        priority
                        src={item.imageUrl ?? ''}
                        style={{ objectFit: 'contain' }}
                      />
                    </div>
                  </div>
                </div>
              );
            })}
          </Carousel>
        </div>
      ) : (
        <div />
      )}
    </div>
  );
};

const RotationBanner = ({ device, location, bannerItem }: PropsComponentBanner) => {
  const dataSize = contentStyle(device, location);
  const randomIndex = Math.floor(Math.random() * Number(bannerItem?.images?.length));
  const itemImageSelect = bannerItem?.images?.[randomIndex];
  const { user } = useSelector((store: RootState) => store.auth);
  const dynamicUrl = getImageDynamicUrl(user);
  const url = dynamicUrl && itemImageSelect?.dynamic_url ? dynamicUrl : itemImageSelect?.url;

  return (
    <div style={{ width: `${dataSize.width}px`, height: `${dataSize.height}px`, margin: '0 auto' }}>
      <div
        style={{
          margin: '0 auto',
          height: `${dataSize.height}px`,
          position: 'relative',
          display: 'flex',
          justifyItems: 'center',
        }}
      >
        <Image
          alt={itemImageSelect?.alt_image ?? ''}
          className={`z-[5] ${url ? 'cursor-pointer ' : ''}`}
          fill={false}
          layout="fill"
          onClick={() => {
            if (url) {
              window.open(url ?? '', '_blank');
            }
          }}
          src={itemImageSelect?.imageUrl ?? ''}
          style={{ objectFit: 'contain' }}
        />
      </div>
    </div>
  );
};

const FixedBanner = ({ device, location, bannerItem }: PropsComponentBanner) => {
  const dataSize = contentStyle(device, location);
  const itemImageSelect = bannerItem?.images?.[0];
  const { user } = useSelector((store: RootState) => store.auth);
  const dynamicUrl = getImageDynamicUrl(user);
  const url = dynamicUrl && itemImageSelect?.dynamic_url ? dynamicUrl : itemImageSelect?.url;

  return (
    <div className="mx-[-20px]">
      <div style={{ width: `${dataSize.width}px`, height: `${dataSize.height}px`, margin: '0 auto' }}>
        <div
          style={{
            margin: '0 auto',
            height: `${dataSize.height}px`,
            position: 'relative',
            display: 'flex',
            justifyItems: 'center',
          }}
        >
          <Image
            alt={itemImageSelect?.alt_image ?? ''}
            className={`z-[5] ${url ? 'cursor-pointer' : ''}`}
            fill={false}
            layout="fill"
            onClick={() => {
              if (url) {
                window.open(url ?? '', '_blank');
              }
            }}
            src={itemImageSelect?.imageUrl ?? ''}
            style={{ objectFit: 'contain' }}
          />
        </div>
      </div>
    </div>
  );
};

const TemplateBanner = ({ banner, device, location }: Props) => {
  const typeBanner = banner?.[0]?.type ?? 'Fixed';

  const renderComponent = () => {
    switch (typeBanner) {
      case 'Carousel':
        return <CarouselBanner bannerItem={banner?.[0]} device={device} location={location} />;
      case 'Rotation':
        return <RotationBanner bannerItem={banner?.[0]} device={device} location={location} />;
      case 'Fixed':
        return <FixedBanner bannerItem={banner?.[0]} device={device} location={location} />;
      default:
        return <FixedBanner bannerItem={banner?.[0]} device={device} location={location} />;
    }
  };
  return <div className=" px-[20px]">{renderComponent()}</div>;
};

export default TemplateBanner;
