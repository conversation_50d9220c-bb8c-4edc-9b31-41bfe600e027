/* eslint-disable @typescript-eslint/no-unused-vars */
import { TypeCampaign } from '@/redux/endpoints/campaign';
import { TypeBannerResponse } from '@/redux/endpoints/banner';
import useWindowDimensions from '@/hooks/useWindowDimensions';
import clsx from 'clsx';
import HeaderHomePage from './HeaderHomePage';
import TemplateCampaign from './TemplateCampaign';
import TemplateBanner from './TemplateBanner';
import GoogleAd from '../GoogleAd';

interface IPageProps {
  campaignsOrderByViews: TypeCampaign[] | null;
  // campaignsOrderByStartTime: TypeCampaign[] | null;
  campaignsOrderByTotalPrizeValue: TypeCampaign[] | null;
  dataBanner: {
    bannerTopPC: TypeBannerResponse['data'] | null;
    bannerTopMO: TypeBannerResponse['data'] | null;
    bannerFooterPC: TypeBannerResponse['data'] | null;
    bannerFooterMO: TypeBannerResponse['data'] | null;
  };
}

function HomePage({
  campaignsOrderByViews,
  // campaignsOrderByStartTime,
  campaignsOrderByTotalPrizeValue,
  dataBanner,
}: IPageProps) {
  const { width } = useWindowDimensions();
  const topBannerClass = clsx({
    'lg:[&>div:first-child]:pt-[72px]': dataBanner?.bannerTopPC && dataBanner?.bannerTopPC?.length > 0 && width > 1024,
    'md:[&>div:first-child]:pt-[72px]': dataBanner?.bannerTopMO && dataBanner?.bannerTopMO?.length > 0 && width <= 1024,
  });

  const bottomBannerClass = clsx({
    'lg:[&>div:last-child]:pb-[72px]':
      dataBanner?.bannerFooterPC && dataBanner?.bannerFooterPC?.length > 0 && width > 1024,
    'md:[&>div:last-child]:pb-[72px]':
      dataBanner?.bannerFooterMO && dataBanner?.bannerFooterMO?.length > 0 && width <= 1024,
  });

  return (
    <div className="font-notoSans bg-white">
      <HeaderHomePage />
      {width >= 1000 ? (
        <GoogleAd
          adClient="ca-pub-2602868131600259"
          // adFormat="auto"
          adSlot="3172651414"
          style={{ width: 970, height: 250, marginTop: 120 }}
        />
      ) : (
        <GoogleAd
          adClient="ca-pub-2602868131600259"
          adSlot="1264752928"
          style={{ height: 250, width: 336, marginTop: 80 }}
        />
      )}

      {width > 1024 && dataBanner?.bannerTopPC && dataBanner?.bannerTopPC?.length > 0 ? (
        <div className="pt-[72px] ">
          <TemplateBanner banner={dataBanner?.bannerTopPC} device="desktop" location="top" />
        </div>
      ) : (
        ''
      )}
      {width <= 1024 && dataBanner?.bannerTopMO && dataBanner?.bannerTopMO?.length > 0 ? (
        <div className="md:pt-[72px] pt-[56px]">
          <TemplateBanner banner={dataBanner?.bannerTopMO} device="mobile" location="top" />
        </div>
      ) : (
        ''
      )}
      <div className={`${topBannerClass} ${bottomBannerClass}`}>
        {campaignsOrderByViews && campaignsOrderByViews?.length > 0 ? (
          <TemplateCampaign
            hideButtonLink
            listCampaign={campaignsOrderByViews}
            title="おすすめのキャンペーン"
            viewMoreLink="/campaigns?orderBy=totalViews"
          />
        ) : (
          ''
        )}
        {/* {campaignsOrderByStartTime && campaignsOrderByStartTime?.length > 0 ? (
        <TemplateCampaign
          bgColor="#D5FFFF"
          listCampaign={campaignsOrderByStartTime}
          title="新着キャンペーン"
          viewMoreLink="/campaigns?orderBy=startTime"
        />
      ) : (
        ''
      )} */}
        {campaignsOrderByTotalPrizeValue && campaignsOrderByTotalPrizeValue?.length > 0 ? (
          <TemplateCampaign
            bgColor="#D5FFFF"
            listCampaign={campaignsOrderByTotalPrizeValue}
            title="高額報酬キャンペーン"
            viewMoreLink="/campaigns?orderBy=totalPrizeValue"
          />
        ) : (
          ''
        )}
      </div>
      {/* <div className="w-full flex justify-center mt-20"> */}
      {width >= 1000 ? (
        <GoogleAd
          adClient="ca-pub-2602868131600259"
          // adFormat="auto"
          adSlot="6227275309"
          style={{ width: 970, height: 90 }}
        />
      ) : (
        <GoogleAd adClient="ca-pub-2602868131600259" adSlot="1121282699" style={{ height: 250, width: 336 }} />
      )}

      {/* </div> */}
      {width > 1024 && dataBanner?.bannerFooterPC && dataBanner?.bannerFooterPC?.length > 0 ? (
        <div className="pt-[72px] mb-[-28px]">
          <TemplateBanner banner={dataBanner?.bannerFooterPC} device="desktop" location="footer" />
        </div>
      ) : (
        ''
      )}
      {width <= 1024 && dataBanner?.bannerFooterMO && dataBanner?.bannerFooterMO?.length > 0 ? (
        <div className="md:pt-[72px] md:mb-[-28px] pt-[56px]">
          <TemplateBanner banner={dataBanner?.bannerFooterMO} device="mobile" location="footer" />
        </div>
      ) : (
        ''
      )}
    </div>
  );
}

export default HomePage;
