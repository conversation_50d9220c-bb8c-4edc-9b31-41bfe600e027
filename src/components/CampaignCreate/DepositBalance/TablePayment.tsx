/* eslint-disable no-console */
/* eslint-disable @typescript-eslint/no-unused-vars */
import React, { useEffect, useMemo, useState } from 'react';
import { Table } from 'antd';
import { useSelector } from 'react-redux';
import { RootState } from '@/redux/store';
import styles from '@/components/common/BasicTable/index.module.scss';
import CircleArrow from '@/components/common/icons/CircleArrow';
import { useRouter } from 'next/router';
import { ColumnsType } from 'antd/es/table';
import { useGetPaymentQuery } from '@/redux/endpoints/payment';
import moment from 'moment';
import { formatNumber } from '@/utils/formatNumber';
import useWindowDimensions from '@/hooks/useWindowDimensions';
import BasicInput from '@/components/common/BasicInput';
// eslint-disable-next-line import/no-extraneous-dependencies
import { useDebounce } from '@uidotdev/usehooks';

interface DataType {
  key: React.Key;
  campaignId: string;
  date: string;
  payment?: string | number;
  withdrawal?: string | number;
  content: string;
  depositBalance: string | number;
}

function TablePayment() {
  const { user } = useSelector((state: RootState) => state.auth);
  const [pageTable, setPageTable] = useState<number>(0);
  const [text, setText] = useState<string>('');
  const paramSearch = useDebounce(text, 500);
  const { width } = useWindowDimensions();
  const { push, query, isReady } = useRouter();
  const { data: dataListPayment, isFetching } = useGetPaymentQuery(
    { skip: pageTable ?? 0, take: 10, q: user?.isSuperAdmin && paramSearch !== '' ? paramSearch : undefined },
    { refetchOnMountOrArgChange: true }
  );

  useEffect(() => {
    if (query.page) {
      setPageTable(Number(Number(query.page) - 1) * 10);
    }
  }, [isReady, query?.page]);

  const columns: ColumnsType<DataType> = useMemo(() => {
    const columnDefault: ColumnsType<DataType> = [
      {
        title: '日付',
        dataIndex: 'date',
      },
      {
        title: '購入額',
        dataIndex: 'payment',
        render: (value) => <span>{value !== undefined ? `${formatNumber(value, true, 1)}円` : '-'} </span>,
      },
      {
        title: '配布済み金額',
        dataIndex: 'withdrawal',
        render: (value) => <span>{value !== undefined ? `${formatNumber(value, true, 1)}円` : '-'} </span>,
      },
      {
        title: '内容',
        dataIndex: 'content',
        render: (value) => <span className="text-[#2675BE]">{value}</span>,
      },
      {
        title: 'デポジット残高',
        dataIndex: 'depositBalance',
        render: (value) => <span>{formatNumber(value, true, 1)}円 </span>,
      },
    ];
    if (user?.isSuperAdmin) {
      columnDefault.unshift({
        title: '組織名',
        dataIndex: 'companyName',
      });
    }
    return columnDefault;
  }, [user?.isSuperAdmin]);

  const data = useMemo<DataType[] | undefined>(
    () =>
      dataListPayment?.payments.map((e) => ({
        key: e.id,
        campaignId: e.campaignId,
        date: moment(e.createdAt).format('YYYY/MM/DD'),
        payment: e.type === 'PAYMENT' ? e.amount : undefined,
        withdrawal: e.type === 'PAYMENT' ? undefined : e.amount,
        content: e.campaignName,
        depositBalance: e.amountAfterTransaction,
        companyName: e.company.name,
        type: e.type,
      })),
    [dataListPayment?.payments]
  );

  return (
    <div className={styles.customTable}>
      {user?.isSuperAdmin && (
        <div className="flex items-center mb-5">
          <p className="font-bold mr-2">組織</p>
          <div className="max-w-[200px]">
            <BasicInput
              className="!h-[40px] !min-h-[40px]"
              onChange={(e) => {
                setText(e.target.value);
              }}
              placeholder="ID/Name"
            />
          </div>
        </div>
      )}
      <Table
        columns={columns}
        dataSource={data}
        loading={isFetching}
        onRow={(record) => ({
          onClick: () => {
            push(`/campaign-creator/list/${record.campaignId}`);
          },
        })}
        pagination={{
          position: ['bottomCenter'],
          pageSize: 10,
          total: dataListPayment?.total,
          showSizeChanger: false,
          jumpNextIcon: <span className="text-[16px] font-medium tracking-[0.48px]">...</span>,
          jumpPrevIcon: <span className="text-[16px] font-medium tracking-[0.48px]">...</span>,
          prevIcon: <CircleArrow position="left" />,
          nextIcon: <CircleArrow />,
          simple: width < 600,
          // eslint-disable-next-line react/no-unstable-nested-components
          showTotal: width < 400 ? undefined : () => <span>{dataListPayment?.total} 件</span>,
          onChange: (page) => {
            push({ query: { page } }, undefined, { shallow: true, scroll: true });
          },
          // current: pageTable,
        }}
        scroll={{ x: 700 }}
        tableLayout="fixed"
      />
    </div>
  );
}

export default TablePayment;
