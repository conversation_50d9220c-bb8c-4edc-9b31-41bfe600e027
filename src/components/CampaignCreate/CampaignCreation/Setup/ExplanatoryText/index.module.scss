@import '../../../../../styles/globals.scss';
.customeTextEditor {
  :global {
    .ql-toolbar.ql-snow {
      padding: 0px 24px;
      height: 56px;
      display: flex;
      align-items: center;
      border: 2px solid #333;
      border-radius: 6px 6px 0 0;
      @include mobile {
        height: fit-content;
        padding: 10px;
      }
    }
    .ql-header.ql-picker {
      width: 120px;
    }
    .ql-editor {
      padding: 32px;
    }
    .ql-container.ql-snow {
      height: 686px;
      border: 2px solid #333;
      border-radius: 0 0 6px 6px;
      border-top: none;
    }
    .ql-snow .ql-stroke {
      fill: inherit;
      stroke-width: 1;
      // stroke: none;
    }

    .ql-active {
      path {
        fill: #04afaf;
        stroke: 2;
      }
    }
    .ql-snow .ql-tooltip[data-mode='link']::before {
      content: 'リンクを入する';
    }
    .ql-snow .ql-tooltip.ql-editing a.ql-action::after {
      content: '保存';
    }
    .ql-snow .ql-tooltip::before {
      content: 'URLにアクセスする';
    }
    .ql-snow .ql-tooltip a.ql-action::after {
      content: '編集';
    }
    .ql-snow .ql-tooltip a.ql-remove::before {
      content: '削除';
    }

    // .quill {
    // }
  }
  .ql-snow .ql-picker.ql-expanded .ql-picker-label .ql-stroke {
    stroke: #000 !important;
    stroke-width: 1;
  }
}
