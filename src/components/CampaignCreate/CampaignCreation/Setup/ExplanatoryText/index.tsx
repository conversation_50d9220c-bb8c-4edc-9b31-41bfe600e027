import dynamic from 'next/dynamic';
import React, { LegacyRef, useEffect, useMemo, useRef } from 'react';
import 'react-quill/dist/quill.snow.css';
import type ReactQuill from 'react-quill';
import styles from './index.module.scss';
import { formats } from './EditorToolbar';

interface IWrappedComponent extends React.ComponentProps<typeof ReactQuill> {
  forwardedRef: LegacyRef<ReactQuill>;
}
const QuillToolbar = dynamic(import('./EditorToolbar'), { ssr: false });
const QuillWrapper = dynamic(
  async () => {
    const { default: RQ } = await import('react-quill');
    const { Quill } = RQ;
    //  Add sizes to whitelist and register them
    const Size = Quill.import('formats/size');
    Size.whitelist = ['extra-small', 'small', 'medium', 'large'];
    Quill.register(Size, true);

    // Add fonts to whitelist and register them
    const Font = Quill.import('formats/font');
    Font.whitelist = ['arial', 'comic-sans', 'courier-new', 'georgia', 'helvetica', 'lucida'];
    Quill.register(Font, true);
    const icons = Quill.import('ui/icons');

    icons.list.bullet = `<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
          <path d="M4.375 7.5C5.41053 7.5 6.25 6.66053 6.25 5.625C6.25 4.58947 5.41053 3.75 4.375 3.75C3.33947 3.75 2.5 4.58947 2.5 5.625C2.5 6.66053 3.33947 7.5 4.375 7.5Z" fill="#212529"/>
          <path d="M4.375 16.25C5.41053 16.25 6.25 15.4105 6.25 14.375C6.25 13.3395 5.41053 12.5 4.375 12.5C3.33947 12.5 2.5 13.3395 2.5 14.375C2.5 15.4105 3.33947 16.25 4.375 16.25Z" fill="#212529"/>
          <path d="M10 13.75H18.75V15H10V13.75ZM10 5H18.75V6.25H10V5Z" fill="#212529"/>
        </svg>`;
    icons.list.ordered = `<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
         <path d="M10 13.75H18.75V15H10V13.75ZM10 5H18.75V6.25H10V5ZM5 7.5V2.5H3.75V3.125H2.5V4.375H3.75V7.5H2.5V8.75H6.25V7.5H5ZM6.25 17.5H2.5V15C2.5 14.6685 2.6317 14.3505 2.86612 14.1161C3.10054 13.8817 3.41848 13.75 3.75 13.75H5V12.5H2.5V11.25H5C5.33152 11.25 5.64946 11.3817 5.88388 11.6161C6.1183 11.8505 6.25 12.1685 6.25 12.5V13.75C6.25 14.0815 6.1183 14.3995 5.88388 14.6339C5.64946 14.8683 5.33152 15 5 15H3.75V16.25H6.25V17.5Z" fill="#212529"/>
        </svg>`;
    icons.color = `<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" >
          <rect class='ql-color-label ql-stroke ' width="20" height="20" rx="4" fill="#212529"/>
        </svg>`;
    icons.align[' '] = `<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
          <path d="M7.5 3.75H16.25V5H7.5V3.75ZM7.5 7.5H13.75V8.75H7.5V7.5ZM7.5 11.25H16.25V12.5H7.5V11.25ZM7.5 15H13.75V16.25H7.5V15ZM3.75 2.5H5V17.5H3.75V2.5Z" fill="#212529"/>
        </svg>`;
    icons.undo = `<svg  xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
          <path  d="M12.5 6.25H4.88438L7.12625 4.00875L6.25 3.125L2.5 6.875L6.25 10.625L7.12625 9.74062L4.88625 7.5H12.5C13.4946 7.5 14.4484 7.89509 15.1517 8.59835C15.8549 9.30161 16.25 10.2554 16.25 11.25C16.25 12.2446 15.8549 13.1984 15.1517 13.9017C14.4484 14.6049 13.4946 15 12.5 15H7.5V16.25H12.5C13.8261 16.25 15.0979 15.7232 16.0355 14.7855C16.9732 13.8479 17.5 12.5761 17.5 11.25C17.5 9.92392 16.9732 8.65215 16.0355 7.71447C15.0979 6.77678 13.8261 6.25 12.5 6.25Z" fill="#212529"/>
        </svg>`;
    icons.redo = `<svg  xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
          <path  d="M7.5 6.25H15.1156L12.8737 4.00875L13.75 3.125L17.5 6.875L13.75 10.625L12.8737 9.74062L15.1137 7.5H7.5C6.50544 7.5 5.55161 7.89509 4.84835 8.59835C4.14509 9.30161 3.75 10.2554 3.75 11.25C3.75 12.2446 4.14509 13.1984 4.84835 13.9017C5.55161 14.6049 6.50544 15 7.5 15H12.5V16.25H7.5C6.17392 16.25 4.90215 15.7232 3.96447 14.7855C3.02678 13.8479 2.5 12.5761 2.5 11.25C2.5 9.92392 3.02678 8.65215 3.96447 7.71447C4.90215 6.77678 6.17392 6.25 7.5 6.25Z" fill="#212529"/>
        </svg>`;
    icons.bold = `<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
          <path  d="M11.4062 15.625H5.625V4.375H10.9375C11.5639 4.37504 12.1771 4.55435 12.7048 4.89174C13.2325 5.22914 13.6526 5.71052 13.9155 6.27903C14.1784 6.84754 14.2731 7.47942 14.1884 8.10001C14.1037 8.72061 13.8431 9.30399 13.4375 9.78125C13.9673 10.205 14.3528 10.7825 14.5408 11.4344C14.7289 12.0862 14.7102 12.7803 14.4875 13.4211C14.2647 14.0619 13.8488 14.6179 13.297 15.0126C12.7452 15.4073 12.0847 15.6213 11.4062 15.625ZM7.5 13.75H11.3937C11.5784 13.75 11.7613 13.7136 11.9319 13.643C12.1025 13.5723 12.2575 13.4687 12.3881 13.3381C12.5187 13.2075 12.6223 13.0525 12.693 12.8819C12.7636 12.7113 12.8 12.5284 12.8 12.3438C12.8 12.1591 12.7636 11.9762 12.693 11.8056C12.6223 11.635 12.5187 11.48 12.3881 11.3494C12.2575 11.2188 12.1025 11.1152 11.9319 11.0445C11.7613 10.9739 11.5784 10.9375 11.3937 10.9375H7.5V13.75ZM7.5 9.0625H10.9375C11.1222 9.0625 11.305 9.02613 11.4756 8.95546C11.6463 8.88478 11.8013 8.7812 11.9319 8.65062C12.0625 8.52004 12.166 8.36501 12.2367 8.1944C12.3074 8.02378 12.3438 7.84092 12.3438 7.65625C12.3438 7.47158 12.3074 7.28872 12.2367 7.1181C12.166 6.94749 12.0625 6.79246 11.9319 6.66188C11.8013 6.5313 11.6463 6.42772 11.4756 6.35704C11.305 6.28637 11.1222 6.25 10.9375 6.25H7.5V9.0625Z" fill="#212529"/>
        </svg>`;
    icons.italic = `<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
          <path d="M15.625 5.625V4.375H7.5V5.625H10.7125L7.98125 14.375H4.375V15.625H12.5V14.375H9.2875L12.0187 5.625H15.625Z" fill="#212529"/>
        </svg>`;
    icons.underline = `<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
          <path d="M2.5 16.25H17.5V17.5H2.5V16.25ZM10 14.375C8.83968 14.375 7.72688 13.9141 6.90641 13.0936C6.08594 12.2731 5.625 11.1603 5.625 10V3.125H6.875V10C6.875 10.8288 7.20424 11.6237 7.79029 12.2097C8.37634 12.7958 9.1712 13.125 10 13.125C10.8288 13.125 11.6237 12.7958 12.2097 12.2097C12.7958 11.6237 13.125 10.8288 13.125 10V3.125H14.375V10C14.375 11.1603 13.9141 12.2731 13.0936 13.0936C12.2731 13.9141 11.1603 14.375 10 14.375Z" fill="#212529"/>
        </svg>`;
    icons.strike = `<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
          <path d="M17.5 9.37538H11.2225C10.945 9.30076 10.6662 9.23096 10.3863 9.16601C8.63125 8.75101 7.63875 8.44726 7.63875 7.02663C7.6245 6.78139 7.66081 6.53584 7.74542 6.30522C7.83004 6.0746 7.96115 5.86384 8.13062 5.68601C8.6615 5.24944 9.32644 5.00889 10.0137 5.00476C11.7825 4.96101 12.5981 5.56101 13.265 6.47351L14.2744 5.73601C13.8019 5.05748 13.1578 4.51654 12.4078 4.16845C11.6578 3.82036 10.8288 3.6776 10.0056 3.75476C8.99439 3.76121 8.01887 4.12947 7.25563 4.79288C6.96634 5.08632 6.74024 5.4359 6.59125 5.82008C6.44227 6.20426 6.37356 6.61488 6.38937 7.02663C6.36197 7.47718 6.4466 7.92751 6.63572 8.33737C6.82483 8.74723 7.11254 9.10385 7.47312 9.37538H2.5V10.6254H11.0325C12.2619 10.9816 12.9969 11.4454 13.0156 12.7241C13.0359 12.9973 12.9985 13.2717 12.9056 13.5294C12.8128 13.7871 12.6667 14.0223 12.4769 14.2198C11.8155 14.7411 10.9938 15.017 10.1519 15.0004C9.52345 14.9822 8.90738 14.8213 8.35029 14.5299C7.7932 14.2385 7.30966 13.8243 6.93625 13.3185L5.97812 14.121C6.46358 14.768 7.08994 15.2959 7.80972 15.6648C8.52951 16.0338 9.32384 16.234 10.1325 16.2504H10.195C11.3492 16.2636 12.4695 15.86 13.35 15.1135C13.6625 14.7984 13.9054 14.4213 14.0632 14.0065C14.2209 13.5917 14.2898 13.1485 14.2656 12.7054C14.289 11.9474 14.0332 11.2072 13.5469 10.6254H17.5V9.37538Z" fill="#212529"/>
        </svg>`;
    icons.link = `<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
          <path d="M18.2813 4.22405C17.9329 3.87445 17.5189 3.59706 17.0631 3.40779C16.6073 3.21852 16.1186 3.12109 15.625 3.12109C15.1315 3.12109 14.6428 3.21852 14.187 3.40779C13.7312 3.59706 13.3172 3.87445 12.9688 4.22405L13.8563 5.11155C14.089 4.87886 14.3652 4.69429 14.6692 4.56836C14.9733 4.44243 15.2991 4.37762 15.6282 4.37762C15.9572 4.37762 16.2831 4.44243 16.5871 4.56836C16.8911 4.69429 17.1674 4.87886 17.4 5.11155C17.6327 5.34424 17.8173 5.62047 17.9432 5.92449C18.0692 6.22851 18.134 6.55436 18.134 6.88343C18.134 7.21249 18.0692 7.53834 17.9432 7.84236C17.8173 8.14638 17.6327 8.42261 17.4 8.6553L12.4 13.6553C11.9309 14.1252 11.2944 14.3896 10.6304 14.3901C9.96638 14.3907 9.32935 14.1275 8.85942 13.6584C8.38949 13.1893 8.12515 12.5528 8.12457 11.8888C8.12398 11.2248 8.38719 10.5877 8.85629 10.1178L9.73754 9.2303L8.85629 8.3428L7.96879 9.2303C7.61919 9.57869 7.3418 9.99266 7.15253 10.4485C6.96326 10.9043 6.86583 11.393 6.86583 11.8865C6.86583 12.3801 6.96326 12.8688 7.15253 13.3246C7.3418 13.7804 7.61919 14.1944 7.96879 14.5428C8.67597 15.2409 9.63134 15.6298 10.625 15.624C11.1205 15.6261 11.6114 15.5299 12.0695 15.3411C12.5276 15.1523 12.9437 14.8746 13.2938 14.524L18.2938 9.52405C18.9944 8.81927 19.3866 7.86522 19.3842 6.87146C19.3819 5.87771 18.9852 4.92552 18.2813 4.22405Z" fill="#212529"/>
          <path d="M2.61879 15.5115C2.38541 15.2793 2.20022 15.0031 2.07386 14.6991C1.94749 14.395 1.88244 14.069 1.88244 13.7397C1.88244 13.4104 1.94749 13.0844 2.07386 12.7803C2.20022 12.4762 2.38541 12.2001 2.61879 11.9678L7.61879 6.9678C7.85109 6.73442 8.1272 6.54923 8.43127 6.42287C8.73534 6.2965 9.06138 6.23145 9.39067 6.23145C9.71995 6.23145 10.046 6.2965 10.3501 6.42287C10.6541 6.54923 10.9302 6.73442 11.1625 6.9678C11.3944 7.20193 11.577 7.48022 11.6994 7.78619C11.8218 8.09215 11.8815 8.41958 11.875 8.74905C11.8769 9.07955 11.8133 9.40715 11.6878 9.7129C11.5623 10.0186 11.3774 10.2965 11.1438 10.5303L9.81879 11.8741L10.7063 12.7615L12.0313 11.4365C12.7366 10.7312 13.1328 9.77463 13.1328 8.77718C13.1328 7.77972 12.7366 6.82311 12.0313 6.1178C11.326 5.41249 10.3694 5.01625 9.37192 5.01625C8.37446 5.01625 7.41785 5.41249 6.71254 6.1178L1.71254 11.1178C1.362 11.4663 1.08382 11.8807 0.893994 12.3371C0.704168 12.7935 0.606445 13.2829 0.606445 13.7772C0.606445 14.2715 0.704168 14.7609 0.893994 15.2173C1.08382 15.6737 1.362 16.088 1.71254 16.4365C2.42431 17.1293 3.38185 17.5115 4.37504 17.499C5.37698 17.5 6.33862 17.1046 7.05004 16.399L6.16254 15.5115C5.93025 15.7449 5.65413 15.9301 5.35006 16.0565C5.04599 16.1829 4.71995 16.2479 4.39067 16.2479C4.06138 16.2479 3.73534 16.1829 3.43127 16.0565C3.1272 15.9301 2.85109 15.7449 2.61879 15.5115Z" fill="#212529"/>
        </svg>`;
    icons.image = `<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
          <path d="M11.875 8.75C12.2458 8.75 12.6084 8.64003 12.9167 8.43401C13.225 8.22798 13.4654 7.93514 13.6073 7.59253C13.7492 7.24992 13.7863 6.87292 13.714 6.50921C13.6416 6.14549 13.463 5.8114 13.2008 5.54917C12.9386 5.28695 12.6045 5.10837 12.2408 5.03603C11.8771 4.96368 11.5001 5.00081 11.1575 5.14273C10.8149 5.28464 10.522 5.52496 10.316 5.83331C10.11 6.14165 10 6.50416 10 6.875C10 7.37228 10.1975 7.84919 10.5492 8.20083C10.9008 8.55246 11.3777 8.75 11.875 8.75ZM11.875 6.25C11.9986 6.25 12.1195 6.28666 12.2222 6.35533C12.325 6.42401 12.4051 6.52162 12.4524 6.63582C12.4997 6.75003 12.5121 6.87569 12.488 6.99693C12.4639 7.11817 12.4044 7.22953 12.3169 7.31694C12.2295 7.40435 12.1182 7.46388 11.9969 7.48799C11.8757 7.51211 11.75 7.49973 11.6358 7.45243C11.5216 7.40512 11.424 7.32501 11.3553 7.22223C11.2867 7.11945 11.25 6.99861 11.25 6.875C11.25 6.70924 11.3158 6.55027 11.4331 6.43306C11.5503 6.31585 11.7092 6.25 11.875 6.25Z" fill="#212529"/>
          <path d="M16.25 2.5H3.75C3.41848 2.5 3.10054 2.6317 2.86612 2.86612C2.6317 3.10054 2.5 3.41848 2.5 3.75V16.25C2.5 16.5815 2.6317 16.8995 2.86612 17.1339C3.10054 17.3683 3.41848 17.5 3.75 17.5H16.25C16.5815 17.5 16.8995 17.3683 17.1339 17.1339C17.3683 16.8995 17.5 16.5815 17.5 16.25V3.75C17.5 3.41848 17.3683 3.10054 17.1339 2.86612C16.8995 2.6317 16.5815 2.5 16.25 2.5ZM16.25 16.25H3.75V12.5L6.875 9.375L10.3688 12.8687C10.603 13.1016 10.9198 13.2322 11.25 13.2322C11.5802 13.2322 11.897 13.1016 12.1313 12.8687L13.125 11.875L16.25 15V16.25ZM16.25 13.2312L14.0063 10.9875C13.772 10.7547 13.4552 10.624 13.125 10.624C12.7948 10.624 12.478 10.7547 12.2437 10.9875L11.25 11.9812L7.75625 8.4875C7.52205 8.25469 7.20523 8.12401 6.875 8.12401C6.54477 8.12401 6.22795 8.25469 5.99375 8.4875L3.75 10.7312V3.75H16.25V13.2312Z" fill="#212529"/>
        </svg>`;
    icons.blockquote = `<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
          <path d="M7.5 9.375H3.81875C3.93491 8.60146 4.21112 7.86066 4.62974 7.1999C5.04837 6.53914 5.6002 5.97295 6.25 5.5375L7.36875 4.7875L6.68125 3.75L5.5625 4.5C4.6208 5.12755 3.84857 5.97785 3.31433 6.97545C2.7801 7.97305 2.50038 9.08711 2.5 10.2188V14.375C2.5 14.7065 2.6317 15.0245 2.86612 15.2589C3.10054 15.4933 3.41848 15.625 3.75 15.625H7.5C7.83152 15.625 8.14946 15.4933 8.38388 15.2589C8.6183 15.0245 8.75 14.7065 8.75 14.375V10.625C8.75 10.2935 8.6183 9.97554 8.38388 9.74112C8.14946 9.5067 7.83152 9.375 7.5 9.375ZM16.25 9.375H12.5688C12.6849 8.60146 12.9611 7.86066 13.3797 7.1999C13.7984 6.53914 14.3502 5.97295 15 5.5375L16.1188 4.7875L15.4375 3.75L14.3125 4.5C13.3708 5.12755 12.5986 5.97785 12.0643 6.97545C11.5301 7.97305 11.2504 9.08711 11.25 10.2188V14.375C11.25 14.7065 11.3817 15.0245 11.6161 15.2589C11.8505 15.4933 12.1685 15.625 12.5 15.625H16.25C16.5815 15.625 16.8995 15.4933 17.1339 15.2589C17.3683 15.0245 17.5 14.7065 17.5 14.375V10.625C17.5 10.2935 17.3683 9.97554 17.1339 9.74112C16.8995 9.5067 16.5815 9.375 16.25 9.375Z" fill="#212529"/>
        </svg>`;

    // eslint-disable-next-line func-names
    return function ({ forwardedRef, ...props }: IWrappedComponent) {
      return <RQ ref={forwardedRef} {...props} />;
    };
  },
  {
    ssr: false,
    loading: () => <p>Loading ...</p>,
  }
);
interface Props {
  value?: string;
  onChange?: (value: string | null) => void;
}
function ExplanatoryText(props: Props) {
  const { value, onChange } = props;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const quillRef = useRef<any>();
  useEffect(() => {
    if (value === '<p><br></p>') {
      onChange?.(null);
    }
  }, [value]);

  const modules = useMemo(
    () => ({
      history: {
        delay: 2000,
        maxStack: 300,
        userOnly: true,
      },

      toolbar: {
        container: '#toolbar',
        handlers: {
          undo: () => {
            if (quillRef?.current) {
              quillRef?.current?.getEditor()?.history?.undo();
            }
          },
          redo: () => {
            if (quillRef?.current) {
              quillRef?.current?.getEditor()?.history?.redo();
            }
          },
        },
      },
    }),
    []
  );

  return (
    <div className={styles.customeTextEditor}>
      <QuillToolbar />
      <QuillWrapper
        formats={formats}
        forwardedRef={quillRef}
        modules={modules}
        onChange={onChange}
        theme="snow"
        value={value}
      />
    </div>
  );
}
ExplanatoryText.defaultProps = {
  value: '',
  onChange: undefined,
};
export default ExplanatoryText;
