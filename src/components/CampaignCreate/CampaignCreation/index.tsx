/* eslint-disable no-console */
/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable max-lines */
/* eslint-disable max-lines-per-function */
import InfoCampaignContent from '@/components/CampaignDetail/InfoCampaign/InfoCampaignContent';
import BasicTabs from '@/components/common/BasicTabs';
import CButtonShadow from '@/components/common/CButtonShadow';
import PopupAlert from '@/components/common/PopupAlert';
import { useCampaignApiContext } from '@/context/CampaignApiContext';
import { usePopUpContext } from '@/context/PopUpContext';
import { StepContext, TypeTabContext } from '@/context/TabContext';
import { useDeleteCampaignMutation, useLazyGetUserUpdatedCampaignQuery } from '@/redux/endpoints/campaign';
import { useGetMasterDataQuery } from '@/redux/endpoints/masterData';
import { RootState } from '@/redux/store';
import { TaskType, TypeResponseFormCampaign } from '@/types/campaign.type';
import { CAMPAIGN_CREATION_FORM_NAME as FORM_NAME } from '@/utils/constant/enums';
import { adapterDataReWard, adapterDataTask } from '@/utils/func/adapterCampaignParams';
import { getMasterDataLabel } from '@/utils/func/convertCampaign';
import toastMessage from '@/utils/func/toastMessage';
import type { TabsProps } from 'antd';
import { Form, Spin, Tooltip } from 'antd';
import { FormInstance } from 'antd/lib/form/Form';
import dayjs from 'dayjs';
import moment from 'moment';
import { useRouter } from 'next/router';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useSelector } from 'react-redux';
import { copyFunc } from '@/utils/copyFunc';
import { useLazyMeQuery } from '@/redux/endpoints/auth';
import Confirmation from './Confirmation';
import ReWard from './ReWard';
import SaveWarningPopup from './SaveWarningPopup';
import Setup from './Setup';
import Task from './Task';
import styles from './index.module.scss';

function CampaignCreation() {
  const router = useRouter();
  const { accessToken, user } = useSelector((state: RootState) => state.auth);
  const [tab, setTab] = useState<string>('1');

  const { handleCreateCampaignV1, handleUpdateCampaignV1, isFetchingInitData, dataCampaignInfo } =
    useCampaignApiContext();
  const { openPopUp, closeAllPopUp } = usePopUpContext();
  const [deleteCampaign] = useDeleteCampaignMutation();
  const { data: dataMaster } = useGetMasterDataQuery();
  const [triggerMe] = useLazyMeQuery();

  const [getUserUpdatedCampaignQuery] = useLazyGetUserUpdatedCampaignQuery();

  const [isEnableFormConfirm, setIsEnableFormConfirm] = useState(false);

  const formSetupRef = useRef<FormInstance>(null);
  const formTasksRef = useRef<FormInstance>(null);
  const formRewardsRef = useRef<FormInstance>(null);
  const formConfirmRef = useRef<FormInstance>(null);

  const setupFormWatch = Form.useWatch([], formSetupRef?.current ? formSetupRef?.current : undefined);
  const tasksFormWatch = Form.useWatch([], formTasksRef?.current ? formTasksRef?.current : undefined);
  const rewardsFormWatch = Form.useWatch([], formRewardsRef?.current ? formRewardsRef?.current : undefined);
  const isEditPage = useMemo(() => router.pathname.startsWith('/campaign-creator/edit/'), [router.pathname]);

  const onInitConfirmationFormValue = (queryParams: TypeResponseFormCampaign) => {
    try {
      // PASS VALUE TO FROM CONFIRM
      let statusCampaign = '下書き';
      switch (dataCampaignInfo?.status) {
        case 'DRAFT':
          statusCampaign = '下書き';
          break;
        case 'UNDER_REVIEW':
          statusCampaign = '審査中';
          break;
        case 'WAITING_FOR_PUBLICATION':
          statusCampaign = '公開待ち';
          break;
        case 'PUBLIC':
          statusCampaign = '公開中';
          break;
        case 'COMPLETION':
          statusCampaign = '完了';
          break;
        default:
          statusCampaign = '下書き';
          break;
      }
      formConfirmRef?.current?.setFieldsValue({
        nameCampagin: queryParams?.campainName ?? '',
        typeCampagin: dataMaster?.CATEGORY_CAMPAIGN?.find((e) => e.value === queryParams.category)?.label ?? '',
        dateCampagin: !queryParams?.noDate
          ? moment(String(queryParams?.startDate)).format('YYYY/MM/DD HH:mm')
          : `${moment(String(queryParams?.startDate)).format('YYYY/MM/DD HH:mm')} 〜 ${moment(
              String(queryParams?.endDate)
            ).format('YYYY/MM/DD  HH:mm')} `,
        typeWinner: queryParams?.typeWinner ?? '',
        status:
          dataCampaignInfo?.methodOfselectWinners === 'AUTO_PRIZEE_DRAW' &&
          dataCampaignInfo?.status === 'DRAFT' &&
          dataCampaignInfo?.isWaitingPurcare === true
            ? '購入待ち'
            : statusCampaign,
        campaginCreator: user?.email?.email ?? '',
      });
      if (queryParams.typeWinner === 'AUTO_PRIZEE_DRAW') {
        formConfirmRef?.current?.setFieldValue('tableReward', queryParams?.reWard);
        formConfirmRef?.current?.setFieldValue('price', queryParams?.totalReWard);
        formConfirmRef?.current?.setFieldValue('totalWinner', queryParams?.numberOfParticipants);
      } else {
        formConfirmRef?.current?.setFieldValue('compensationSummary', queryParams?.compensationSummary);
      }
    } catch (err) {
      console.log(err);
    }
  };

  const onChangeTab = (newTab: string) => {
    try {
      if (newTab === '4') {
        let queryParams: TypeResponseFormCampaign = {};
        queryParams = {
          ...formSetupRef?.current?.getFieldsValue(),
          ...formTasksRef?.current?.getFieldsValue(),
          ...formConfirmRef?.current?.getFieldsValue(),
          ...formRewardsRef?.current?.getFieldsValue(),
        };

        onInitConfirmationFormValue(queryParams);
      }
    } catch (err) {
      console.log(err);
    }
    setTab(newTab);
    // switch (tab) {
    //   case '1': {
    //     onValidateTab(formSetupRef?.current, newTab);
    //     break;
    //   }
    //   case '2': {
    //     onValidateTab(formTasksRef?.current, newTab);
    //     break;
    //   }
    //   case '3': {
    //     onValidateTab(formRewardsRef?.current, newTab);
    //     break;
    //   }
    //   default:
    //     setTab(newTab);
    //     break;
    // }
  };

  const valueContext = useMemo<TypeTabContext>(
    () => ({
      prevTab: () => {
        setTab(String(Number(tab) - 1));
      },
    }),
    [tab]
  );

  useEffect(() => {
    try {
      const publicSteps = ['1', '2', '3'];
      const protectedSteps = ['4'];

      const step: string = (router?.query?.step ?? '') as string;
      if (router.pathname.startsWith('/campaign-creator/create/draft/')) {
        if (['1', '2', '3', '4'].includes(step)) {
          if (protectedSteps.includes(step)) {
            if (user?.companyRole?.membership !== 'MANAGER') {
              setTab('1');
            } else {
              setTab(step);
            }
          } else if (publicSteps.includes(step)) {
            setTab(step);
          }
          router.replace(
            `/campaign-creator/create/draft/${router.query.id}`,
            `/campaign-creator/create/draft/${router.query.id}`,
            { shallow: true }
          );
        }
      }
      if (isEditPage) {
        if (['1', '2', '3', '4'].includes(step)) {
          if (protectedSteps.includes(step)) {
            if (user?.companyRole?.membership !== 'MANAGER') {
              setTab('1');
            } else {
              setTab(step);
            }
          } else if (publicSteps.includes(step)) {
            setTab(step);
          }
          router.replace(`/campaign-creator/edit/${router.query.id}`, `/campaign-creator/edit/${router.query.id}`, {
            shallow: true,
          });
        }
      }
    } catch (err) {
      console.error(err);
    }
  }, [router, user]);

  const validateFnc = useCallback(async (promises: Promise<any>[]) => {
    let result = false;
    try {
      await Promise.all([...promises]);
      result = true;
    } catch (err) {
      result = false;
    }
    return result;
  }, []);

  const items = useMemo<TabsProps['items']>(
    () => [
      {
        key: '1',
        label: 'セットアップ',
        children: <Setup formInstance={formSetupRef} formName={FORM_NAME.CAMPAIGN_SETUP_INFO} />,
        forceRender: true,
        destroyInactiveTabPane: true,
      },
      {
        key: '2',
        label: 'タスク',
        children: <Task formInstance={formTasksRef} formName={FORM_NAME.CAMPAIGN_SETUP_TASKS} />,
        forceRender: true,
        destroyInactiveTabPane: true,
      },
      {
        key: '3',
        label: (
          <Tooltip placement="bottom" title={isEditPage ? '報酬・確認・購入タブの編集はできません' : ''}>
            <div className="flex">報酬</div>
          </Tooltip>
        ),
        children: <ReWard formInstance={formRewardsRef} formName={FORM_NAME.CAMPAIGN_SETUP_REWARDS} />,
        forceRender: true,
        destroyInactiveTabPane: true,
        disabled: isEditPage,
      },
      {
        key: '4',
        label: (
          <Tooltip placement="bottom" title={isEditPage ? '報酬・確認・購入タブの編集はできません' : ''}>
            <div className="flex">確認・購入</div>
          </Tooltip>
        ),
        children: <Confirmation formInstance={formConfirmRef} formName={FORM_NAME.CAMPAIGN_CONFIRMATION} />,
        forceRender: true,
        destroyInactiveTabPane: true,
        disabled: user?.companyRole?.membership !== 'MANAGER' || isEnableFormConfirm === false || isEditPage,
      },
    ],
    [user?.companyRole?.membership, isEnableFormConfirm]
  );
  const [isClientClicked, setIsClientClicked] = useState(false);

  const handleSubmitForm = async (name: string, { forms }: { forms: any }) => {
    try {
      if (isClientClicked) {
        return;
      }
      setIsClientClicked(true);

      const setUpForm = forms?.[FORM_NAME.CAMPAIGN_SETUP_INFO];
      const taskForm = forms?.[FORM_NAME.CAMPAIGN_SETUP_TASKS];
      const rewardForm = forms?.[FORM_NAME.CAMPAIGN_SETUP_REWARDS];
      const confirmForm = forms?.[FORM_NAME.CAMPAIGN_CONFIRMATION];

      const isPassedSetUpForm = await validateFnc([setUpForm.validateFields({ validateOnly: true })]);
      const isPassedTaskForm = await validateFnc([taskForm.validateFields({ validateOnly: true })]);
      const isPassedRewardForm = await validateFnc([rewardForm.validateFields({ validateOnly: true })]);
      const redirectWhenFailed = () => {
        if (!isPassedSetUpForm) {
          router.push(`${window.location.href}?step=1`);
        } else if (!isPassedTaskForm) {
          router.push(`${window.location.href}?step=2`);
        } else if (!isPassedRewardForm) {
          router.push(`${window.location.href}?step=3`);
        }
      };
      const isPassed = isPassedSetUpForm && isPassedTaskForm && isPassedRewardForm;

      let queryParams: TypeResponseFormCampaign = {};
      queryParams = {
        ...setUpForm?.getFieldsValue(),
        ...taskForm?.getFieldsValue(),
        ...confirmForm?.getFieldsValue(),
        ...rewardForm?.getFieldsValue(),
      };

      onInitConfirmationFormValue(queryParams);

      switch (name) {
        case FORM_NAME.COPPY_CAMPAIGN_LINK: {
          if (router?.query?.id) {
            if (isPassed) {
              await handleUpdateCampaignV1?.(
                String(router?.query?.id),
                queryParams,
                dataCampaignInfo?.status ?? 'DRAFT',
                name,
                isPassed
              );
              const url = `${window.location.origin}/campaigns/${String(router?.query?.id)}`;
              copyFunc(url);
            } else {
              toastMessage(
                '未入力の箇所があります。必須項目をすべて入力してキャンペーン制作を完了してください。',
                'error'
              );
              redirectWhenFailed();
            }
          } else {
            toastMessage('下書き保存後に、リンクをコピーできます。', 'error');
          }

          break;
        }
        case FORM_NAME.CAMPAIGN_DELETE: {
          if (router?.query?.id) {
            openPopUp({
              contents: (
                <PopupAlert
                  message="本当に削除してもよろしいですか？"
                  onOk={() => {
                    deleteCampaign({ campaignId: String(router?.query?.id) })
                      .unwrap()
                      .then(() => {
                        toastMessage('キャンペーンを削除されました。', 'success');
                        router.push('/campaign-creator/list');
                      });
                  }}
                />
              ),
            });
          } else {
            router.push('/campaign-creator/list');
          }
          break;
        }
        case FORM_NAME.CAMPAIGN_PREVIEW: {
          if (isPassed) {
            const typeWinner = rewardForm.getFieldValue('typeWinner');
            if (typeWinner === 'AUTO_PRIZEE_DRAW') {
              const totalReward = rewardForm.getFieldValue('totalReWard');
              if (Number(totalReward) === 0) {
                toastMessage('キャンペーンには少なくとも 1 つの賞品が必要です。', 'error');
                return;
              }
              if (Number(totalReward) > 500000) {
                toastMessage('賞品金額の合計は50万円以下に設定してください', 'error');
                return;
              }
            }

            const isCampaignExpired = false;
            const campaignCategory = getMasterDataLabel(dataMaster, 'CATEGORY_CAMPAIGN', queryParams?.category ?? '');
            const campaignDetail = {
              company: {
                ...user?.memberCompany,
              },
              category: queryParams?.category ?? '',
              title: queryParams?.campainName ?? '',
              description: queryParams.explanatoryText ?? '',
              expiredTime: !queryParams.noDate ? undefined : String(queryParams.endDate),
              setExpiredTime: queryParams.noDate ?? false,
              startTime: String(queryParams.startDate),
              methodOfselectWinners: queryParams.typeWinner,
              totalNumberOfUsersAllowedToWork: queryParams.numberOfParticipants ?? undefined,
              noteReward: queryParams.compensationSummary ?? '',
              image: {
                imageUrl: queryParams?.thumbnail?.imageUrl
                  ? queryParams?.thumbnail?.imageUrl
                  : URL.createObjectURL(queryParams?.thumbnail),
              },
            };
            const campaignRewards = [...adapterDataReWard(queryParams)];
            const campaignTasks = [...adapterDataTask(queryParams)];

            openPopUp({
              contents: (
                <div className="h-[90vh] max-w-[575px] overflow-hidden p-[16px]">
                  <div className="h-full overflow-y-auto">
                    <Spin spinning={isClientClicked}>
                      <div className="flex justify-end my-[16px] mr-[8px]">
                        <div className=" w-[223px] h-[56px]">
                          <CButtonShadow
                            classBgColor="bg-white"
                            classRounded="rounded-[6px]"
                            classShadowColor="bg-main-text"
                            onClick={async () => {
                              try {
                                if (router?.query?.id) {
                                  const url = `${window.location.origin}/campaigns/${String(router?.query?.id)}`;
                                  copyFunc(url);
                                } else {
                                  toastMessage('下書き保存後、リンクをコピーできます。', 'error');
                                }
                              } catch (e) {
                                console.log(e);
                              } finally {
                                closeAllPopUp();
                              }
                            }}
                            shadowSize="normal"
                            textClass='"bg-main-text"'
                            title="リンクをコピー"
                            withIcon={{
                              position: 'left',
                              icon: (
                                <svg
                                  fill="none"
                                  height="18"
                                  viewBox="0 0 18 18"
                                  width="18"
                                  xmlns="http://www.w3.org/2000/svg"
                                >
                                  <path
                                    d="M12 15V16.25C12 16.6875 11.6562 17 11.25 17H2.75C2.3125 17 2 16.6875 2 16.25V4.75C2 4.34375 2.3125 4 2.75 4H5V13.25C5 14.2188 5.78125 15 6.75 15H12ZM12 4.25C12 4.6875 12.3125 5 12.75 5H16V13.25C16 13.6875 15.6562 14 15.25 14H6.75C6.3125 14 6 13.6875 6 13.25V1.75C6 1.34375 6.3125 1 6.75 1H12V4.25ZM15.75 3.28125C15.9062 3.4375 16 3.625 16 3.8125V4H13V1H13.1875C13.375 1 13.5625 1.09375 13.7188 1.25L15.75 3.28125Z"
                                    fill="#333333"
                                  />
                                </svg>
                              ),
                            }}
                          />
                        </div>
                      </div>
                      <div>
                        <InfoCampaignContent
                          bannerUnderCampaign={[]}
                          campaignCategory={campaignCategory}
                          campaignDetail={campaignDetail as any}
                          campaignRewards={campaignRewards as any}
                          campaignTasks={campaignTasks as any}
                          isCampaignExpired={isCampaignExpired}
                          isPreview
                        />
                      </div>
                    </Spin>
                  </div>
                </div>
              ),
            });
          } else {
            toastMessage(
              '未入力の箇所があります。必須項目をすべて入力してキャンペーン制作を完了してください。',
              'error'
            );
            redirectWhenFailed();
          }
          break;
        }
        case FORM_NAME.CAMPAIGN_SAVE_DRAFT: {
          if (router?.query?.id) {
            await handleUpdateCampaignV1?.(
              String(router?.query?.id),
              queryParams,
              dataCampaignInfo?.status ?? 'DRAFT',
              name,
              isPassed
            );
          } else {
            await handleCreateCampaignV1?.(queryParams, 'DRAFT', name, isPassed);
          }
          break;
        }
        case FORM_NAME.CAMPAIGN_CONFIRMATION: {
          if (!isPassed) {
            toastMessage(
              '未入力の箇所があります。必須項目をすべて入力してキャンペーン制作を完了してください。',
              'error'
            );
            redirectWhenFailed();

            return;
          }
          const typeWinner = rewardForm.getFieldValue('typeWinner');

          if (dayjs(queryParams?.startDate).isValid()) {
            // if (typeWinner === 'AUTO_PRIZEE_DRAW') {
            //   const now = dayjs();
            //   const endOfDay = dayjs().endOf('day');
            //   const remainingTime = endOfDay.diff(now);

            //   const startTimeValid = dayjs(queryParams?.startDate).add(-2, 'day').add(-remainingTime, 'milliseconds');
            //   if (startTimeValid.diff(now) <= 0) {
            //     toastMessage('3日後以降の値で開始日時を設定してください', 'error');
            //     return;
            //   }
            // }
            if (typeWinner === 'MANUAL_SELECTION' || typeWinner === 'AUTO_PRIZEE_DRAW') {
              const now = dayjs();
              const startTimeValid = dayjs(queryParams?.startDate).add(1, 'minute');
              if (startTimeValid.diff(now) <= 0) {
                toastMessage('現在時刻より未来の値で開始日時を設定してください', 'error');
                return;
              }
            }
          }

          if (router?.query?.id) {
            if (queryParams.typeWinner === 'AUTO_PRIZEE_DRAW') {
              await handleUpdateCampaignV1?.(
                String(router?.query?.id),
                queryParams,
                dataCampaignInfo?.status ?? 'DRAFT',
                name,
                isPassed,
                'CREATE'
              );
            } else {
              await handleUpdateCampaignV1?.(
                String(router?.query?.id),
                queryParams,
                'WAITING_FOR_PUBLICATION',
                name,
                isPassed
              );
            }
          } else if (queryParams.typeWinner === 'AUTO_PRIZEE_DRAW') {
            await handleCreateCampaignV1?.(queryParams, 'UNDER_REVIEW', name, isPassed);
          } else {
            await handleCreateCampaignV1?.(queryParams, 'WAITING_FOR_PUBLICATION', name, isPassed);
          }
          break;
        }
        case FORM_NAME.CAMPAIGN_SETUP_INFO:
        case FORM_NAME.CAMPAIGN_SETUP_TASKS:
        case FORM_NAME.CAMPAIGN_SETUP_REWARDS: {
          if (!isPassed && name === FORM_NAME.CAMPAIGN_SETUP_REWARDS) {
            toastMessage(
              '未入力の箇所があります。必須項目をすべて入力してキャンペーン制作を完了してください。',
              'error'
            );
            redirectWhenFailed();

            return;
          }
          if (router?.query?.id) {
            await handleUpdateCampaignV1?.(
              String(router?.query?.id),
              queryParams,
              dataCampaignInfo?.status ?? 'DRAFT',
              name,
              isPassed
            );
          } else {
            await handleCreateCampaignV1?.(queryParams, 'DRAFT', name, isPassed);
          }

          break;
        }

        default:
          break;
      }

      window.scrollTo({ behavior: 'smooth', top: 0 });
    } catch (err) {
      console.log(err);
    } finally {
      setTimeout(() => {
        setIsClientClicked(false);
      }, 1000);
    }
  };

  useEffect(() => {
    if (accessToken) {
      triggerMe();
    }
  }, [accessToken, tab]);

  useEffect(() => {
    (async () => {
      if (formSetupRef?.current && formTasksRef?.current && formRewardsRef?.current) {
        try {
          const validated = await validateFnc([
            formSetupRef?.current?.validateFields({ validateOnly: true }),
            formTasksRef?.current?.validateFields({ validateOnly: true }),
            formRewardsRef?.current?.validateFields({ validateOnly: true }),
          ]);
          const optionsTask = formTasksRef?.current.getFieldValue('optionTasks');
          let isAllRequiredTask = true;
          if (optionsTask && Object.values(optionsTask)) {
            Object.values(optionsTask).forEach((item) => {
              if ((item as TaskType).required !== true) {
                isAllRequiredTask = false;
              }
            });
          }
          setIsEnableFormConfirm(validated && isAllRequiredTask);
        } catch (error) {
          console.log('formSetupRef error');
        }
      } else {
        setIsEnableFormConfirm(false);
      }
    })();
  }, [setupFormWatch, tasksFormWatch, rewardsFormWatch]);

  return (
    <Form.Provider
      onFormFinish={async (name, { forms }) => {
        if (router?.query?.id) {
          const data = await getUserUpdatedCampaignQuery({
            campaignId: router?.query?.id as string,
          }).unwrap();
          const isChangeTimeUpdated =
            (moment(dataCampaignInfo?.timeUpdated).isValid()
              ? new Date(dataCampaignInfo?.timeUpdated as string).getTime()
              : 0) !== (moment(data?.timeUpdated).isValid() ? new Date(data?.timeUpdated).getTime() : 0);

          const condition = Boolean(
            (dataCampaignInfo?.emailUserUpdated || data?.emailUserUpdated) &&
              (dataCampaignInfo?.emailUserUpdated !== data?.emailUserUpdated ||
                (dataCampaignInfo?.emailUserUpdated === data?.emailUserUpdated && isChangeTimeUpdated === true))
          );

          if (condition) {
            openPopUp({
              contents: (
                <SaveWarningPopup
                  email={data?.emailUserUpdated ?? ''}
                  onCancel={() => {
                    closeAllPopUp();
                    router.replace('/campaign-creator/list');
                  }}
                  onConfirm={async () => {
                    await handleSubmitForm(name, { forms });
                    closeAllPopUp();
                  }}
                />
              ),
            });
          } else {
            await handleSubmitForm(name, { forms });
          }
          return;
        }
        await handleSubmitForm(name, { forms });
      }}
    >
      <div className="xl:px-[48px] px-[20px] lg:max-w-[780px] min-w-full xl:max-w-full md:max-w-[700px] ">
        <div className="flex xxl:flex-row flex-col py-[32px] w-full justify-between border-b-2 border-[#2D3648] xxl:space-y-0 space-y-[10px] md:space-y-[16px]">
          <span className=" xl:text-[32px] text-[24px] font-bold">
            {isEditPage ? 'キャンペーン編集' : 'キャンペーン作成'}
          </span>
          <div className="flex md:flex-nowrap flex-wrap md:space-y-0 gap-[8px] items-center">
            {!isEditPage && (
              <Form name={FORM_NAME.CAMPAIGN_DELETE}>
                <div className="xl:w-[135px] w-[90px] xl:h-[56px] h-[46px]">
                  <CButtonShadow
                    classBgColor="bg-white"
                    classRounded="rounded-[6px]"
                    classShadowColor="bg-main-text"
                    shadowSize="normal"
                    textClass="bg-main-text"
                    title="削除"
                    type="submit"
                    withIcon={{
                      position: 'left',
                      icon: (
                        <svg fill="none" height="18" viewBox="0 0 18 18" width="18" xmlns="http://www.w3.org/2000/svg">
                          <path
                            d="M3 15.5V5H15V15.5C15 16.3438 14.3125 17 13.5 17H4.5C3.65625 17 3 16.3438 3 15.5ZM11.5 7.5V14.5C11.5 14.7812 11.7188 15 12 15C12.25 15 12.5 14.7812 12.5 14.5V7.5C12.5 7.25 12.25 7 12 7C11.7188 7 11.5 7.25 11.5 7.5ZM8.5 7.5V14.5C8.5 14.7812 8.71875 15 9 15C9.25 15 9.5 14.7812 9.5 14.5V7.5C9.5 7.25 9.25 7 9 7C8.71875 7 8.5 7.25 8.5 7.5ZM5.5 7.5V14.5C5.5 14.7812 5.71875 15 6 15C6.25 15 6.5 14.7812 6.5 14.5V7.5C6.5 7.25 6.25 7 6 7C5.71875 7 5.5 7.25 5.5 7.5ZM15.5 2C15.75 2 16 2.25 16 2.5V3.5C16 3.78125 15.75 4 15.5 4H2.5C2.21875 4 2 3.78125 2 3.5V2.5C2 2.25 2.21875 2 2.5 2H6.25L6.53125 1.4375C6.65625 1.1875 6.90625 1 7.1875 1H10.7812C11.0625 1 11.3125 1.1875 11.4375 1.4375L11.75 2H15.5Z"
                            fill="#333333"
                          />
                        </svg>
                      ),
                    }}
                  />
                </div>
              </Form>
            )}
            <Form name={FORM_NAME.COPPY_CAMPAIGN_LINK}>
              <div className=" w-[150px] xl:w-[223px] h-[46px] xl:h-[56px]">
                <CButtonShadow
                  classBgColor="bg-white"
                  classRounded="rounded-[6px]"
                  classShadowColor="bg-main-text"
                  shadowSize="normal"
                  textClass='"bg-main-text"'
                  title="リンクをコピー"
                  type="submit"
                  withIcon={{
                    position: 'left',
                    icon: (
                      <svg fill="none" height="18" viewBox="0 0 18 18" width="18" xmlns="http://www.w3.org/2000/svg">
                        <path
                          d="M12 15V16.25C12 16.6875 11.6562 17 11.25 17H2.75C2.3125 17 2 16.6875 2 16.25V4.75C2 4.34375 2.3125 4 2.75 4H5V13.25C5 14.2188 5.78125 15 6.75 15H12ZM12 4.25C12 4.6875 12.3125 5 12.75 5H16V13.25C16 13.6875 15.6562 14 15.25 14H6.75C6.3125 14 6 13.6875 6 13.25V1.75C6 1.34375 6.3125 1 6.75 1H12V4.25ZM15.75 3.28125C15.9062 3.4375 16 3.625 16 3.8125V4H13V1H13.1875C13.375 1 13.5625 1.09375 13.7188 1.25L15.75 3.28125Z"
                          fill="#333333"
                        />
                      </svg>
                    ),
                  }}
                />
              </div>
            </Form>
            <Form name={FORM_NAME.CAMPAIGN_PREVIEW}>
              <div className="xl:w-[184px] w-[120px] xl:h-[56px] h-[46px]">
                <CButtonShadow
                  classBgColor="bg-white"
                  classRounded="rounded-[6px]"
                  classShadowColor="bg-main-text"
                  shadowSize="normal"
                  textClass="bg-main-text"
                  title="プレビュー"
                  type="submit"
                  withIcon={{
                    position: 'left',
                    icon: (
                      <svg fill="none" height="18" viewBox="0 0 18 18" width="18" xmlns="http://www.w3.org/2000/svg">
                        <path
                          d="M15 4.8125V5H11V1H11.1875C11.375 1 11.5625 1.09375 11.7188 1.25L14.75 4.28125C14.9062 4.4375 15 4.625 15 4.8125ZM10.75 6H15V16.25C15 16.6875 14.6562 17 14.25 17H3.75C3.3125 17 3 16.6875 3 16.25V1.75C3 1.34375 3.3125 1 3.75 1H10V5.25C10 5.6875 10.3125 6 10.75 6ZM6.5 6.5C5.6875 6.5 5 7.1875 5 8C5 8.84375 5.6875 9.5 6.5 9.5C7.34375 9.5 8 8.84375 8 8C8 7.1875 7.34375 6.5 6.5 6.5ZM13 14V10.5L11.7812 9.28125C11.625 9.125 11.375 9.125 11.25 9.28125L8 12.5L6.78125 11.2812C6.625 11.125 6.40625 11.125 6.25 11.25L5.03125 12.5L5 14H13Z"
                          fill="#333333"
                        />
                      </svg>
                    ),
                  }}
                />
              </div>
            </Form>
            <Form name={FORM_NAME.CAMPAIGN_SAVE_DRAFT}>
              <div className="xl:w-[184px] w-[150px]  xl:h-[56px] h-[46px]">
                <CButtonShadow
                  classBgColor="bg-main-text"
                  classRounded="rounded-[6px]"
                  classShadowColor="bg-white"
                  shadowSize="normal"
                  title={isEditPage ? '保存' : '下書き保存'}
                  type="submit"
                  withIcon={{
                    position: 'left',
                    icon: (
                      <svg fill="none" height="18" viewBox="0 0 18 18" width="18" xmlns="http://www.w3.org/2000/svg">
                        <path
                          d="M0 3C0 1.90625 0.875 1 2 1H7V5C7 5.5625 7.4375 6 8 6H12V10.375L9.03125 13.3438C8.78125 13.5938 8.59375 13.9062 8.5 14.2812L8.03125 16.1562C7.96875 16.4375 7.96875 16.75 8.0625 17H2C0.875 17 0 16.125 0 15V3ZM8 5V1L12 5H8ZM17.625 8.84375C18.0938 9.3125 18.0938 10.0938 17.625 10.5938L16.6875 11.5312L14.4688 9.3125L15.4062 8.375C15.875 7.90625 16.6875 7.90625 17.1562 8.375L17.625 8.84375ZM9.71875 14.0312L13.7812 10L16 12.2188L11.9375 16.25C11.8125 16.375 11.6562 16.4688 11.5 16.5312L9.59375 17C9.4375 17.0312 9.25 17 9.125 16.875C9 16.75 8.96875 16.5625 9 16.375L9.46875 14.5C9.5 14.3438 9.59375 14.1875 9.71875 14.0312Z"
                          fill="white"
                        />
                      </svg>
                    ),
                  }}
                />
              </div>
            </Form>
          </div>
        </div>
        <div className={styles.basicTabCustom}>
          <Spin spinning={isFetchingInitData || isClientClicked}>
            <StepContext.Provider value={valueContext}>
              <BasicTabs
                activeKey={tab}
                items={items}
                onChange={(e) => {
                  onChangeTab(e);
                }}
              />
            </StepContext.Provider>
          </Spin>
        </div>
      </div>
    </Form.Provider>
  );
}

export default CampaignCreation;
