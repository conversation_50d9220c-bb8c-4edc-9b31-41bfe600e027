/* eslint-disable max-lines-per-function */
/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { useContext, useEffect, useMemo, useState } from 'react';
import { Form, Tooltip } from 'antd';
import SelectLabel from '@/components/common/BasicSelect/SelectLabel';
import BasicInput from '@/components/common/BasicInput';
import { StepContext, TypeTabContext } from '@/context/TabContext';
import CButtonShadow from '@/components/common/CButtonShadow';
import FlagItem from '@/components/common/FlagItem';
import CButtonClassic from '@/components/common/CButtonClassic';
import { useCampaignApiContext } from '@/context/CampaignApiContext';
import Link from 'next/link';
import toastMessage from '@/utils/func/toastMessage';
import { TaskType } from '@/types/campaign.type';
import { CampaignFormName } from '@/utils/constant/enums';
import { useRouter } from 'next/router';
import { FormInstance } from 'antd/lib/form/Form';
import TaskCampaign from './TaskCampaign';
import { TypeTasks } from './type';

function Task({
  formName,
  formInstance,
}: {
  formName: CampaignFormName;
  formInstance: React.MutableRefObject<FormInstance | null>;
}) {
  const [form] = Form.useForm();
  const optionTasksForm = Form.useWatch(['optionTasks'], form);

  const { setTaskIdDeletes, dataCampaignDetailTask } = useCampaignApiContext();
  const { prevTab } = useContext<TypeTabContext>(StepContext);

  const router = useRouter();
  const isEditPage = useMemo(() => router.pathname.startsWith('/campaign-creator/edit/'), [router.pathname]);

  const [numberTask, setNumberTask] = useState<TypeTasks[]>([
    {
      id: 1,
      require: false,
      platForm: {
        name: 'TWITTER',
        type: 'twitter_follow',
      },
    },
  ]);

  const hasTaskRequired = useMemo(() => {
    try {
      const listTask: TaskType[] = Object.values(optionTasksForm);
      if (listTask) {
        const taskRequired = listTask.find((item) => item.required === true);
        if (taskRequired) return true;
      }
      return false;
    } catch (error) {
      return false;
    }
  }, [optionTasksForm]);

  const handleDelete = (id: number) => {
    const taskIdDelete = form.getFieldValue(['optionTasks', `task${id}`]).taskId;
    if (taskIdDelete) {
      setTaskIdDeletes?.((prev) => [...prev, taskIdDelete]);
    }
    setNumberTask((prev) => prev.filter((v) => v.id !== id));
    form.setFieldValue(['optionTasks', `task${id}`], {});
  };

  useEffect(() => {
    if (
      dataCampaignDetailTask &&
      (router?.pathname.startsWith('/campaign-creator/create/draft/') ||
        router?.pathname.startsWith('/campaign-creator/edit/'))
    ) {
      form.setFieldValue(
        ['requireTask', 'taskId'],
        dataCampaignDetailTask.tasks.find((e) => e.taskTemplate.config?.requireTask)?.id
      );
      setNumberTask(
        dataCampaignDetailTask.tasks
          .filter((e) => !e.taskTemplate.config?.requireTask)
          .map((v, i) => ({
            id: i + 1,
            platForm: { name: v?.taskTemplate?.config?.platForm, type: v.taskTemplate.config?.type },
            config: v,
          }))
      );
    }
  }, [dataCampaignDetailTask, router?.pathname]);
  // useEffect(() => {
  //   if (numberTask.length === 9) {
  //     toastMessage('タスクの最大数は10タスクです。（ デフォルトタスク除きます）', 'warning');
  //   }
  // }, [numberTask]);

  return (
    <>
      <div className="bg-white rounded-[16px]  mt-[16px] md:p-[40px] p-[24px]">
        <Form
          form={form}
          name={formName}
          onFinishFailed={() => toastMessage('保存して次へ進む前に、必須項目へ入力してください。', 'error')}
          onValuesChange={(e, values) => {
            const { optionTasks } = values;
            Object?.entries(e.optionTasks ?? {}).forEach(([key, value]: [key: any, value: any]) => {
              if (value?.platForm === 'TWITTER') {
                form.setFieldValue(['optionTasks', `${key}`, 'type'], 'twitter_follow');
              }
              if (value?.platForm === 'TIKTOK') {
                form.setFieldValue(['optionTasks', `${key}`, 'type'], 'letThemWatch');
              }
              if (value?.platForm === 'TELEGRAM') {
                form.setFieldValue(['optionTasks', `${key}`, 'type'], 'joinChannel');
              }
              if (value?.platForm === 'DISCORD') {
                form.setFieldValue(['optionTasks', `${key}`, 'type'], 'discord_invite');
              }
              if (value?.platForm === 'CUSTOM') {
                form.setFieldValue(['optionTasks', `${key}`, 'type'], 'formatSingle');
              }
              if (value?.platForm === 'INSTAGRAM') {
                form.setFieldValue(['optionTasks', `${key}`, 'type'], 'makeYouFollow');
              }
            });

            const arrayCustom = Object.values(optionTasks).filter((v: any) => v.platForm === 'CUSTOM');
            if (arrayCustom.length > 2) {
              toastMessage('自由形式質問は最大2問までです。', 'error');
              const id = Number(
                Object.keys(e.optionTasks)
                  .map((v) => v)[0]
                  .slice(4)
              );
              handleDelete(id);
            }
          }}
          ref={formInstance}
          scrollToFirstError={{ behavior: 'smooth', inline: 'start', block: 'end' }}
          // onFinish={(e) => console.log(e)}
        >
          <div className="hidden">
            <Form.Item name="checkNumberTask" noStyle rules={[{ required: numberTask.length < 1 }]}>
              <FlagItem />
            </Form.Item>
            <Form.Item name={['requireTask', 'taskId']} noStyle rules={[{ required: numberTask.length < 1 }]}>
              <FlagItem />
            </Form.Item>
          </div>
          <div className="flex flex-col md:space-y-[24px] gap-4 pb-[24px]">
            {numberTask.map((e, i) => (
              <TaskCampaign index={i} item={e} key={e.id} onDelete={() => handleDelete(e.id)} showDelete />
            ))}
          </div>
          {numberTask.length < 30 && (
            <Tooltip placement="bottomLeft" title={isEditPage ? 'タスクの追加はできません' : ''}>
              <div>
                <CButtonClassic
                  customClassName="!w-[175px] !h-[48px] !mb-[24px] !rounded-[8px]"
                  isDisable={isEditPage}
                  onClick={() => {
                    setNumberTask([
                      ...numberTask,
                      {
                        id: Number(numberTask[numberTask.length - 1]?.id ?? 0) + 1,
                        isNewTask: true,
                        require: false,
                        platForm: {
                          name: 'twitter',
                          type: 'follow',
                        },
                      },
                    ]);
                  }}
                  title="タスクを追加"
                />
              </div>
            </Tooltip>
          )}
          <div className="border-2 border-[#2D3648] rounded-[8px] md:p-[32px] p-[22px]">
            <span>
              必須タスク ※このタスクは削除できません。詳細は
              <Link
                className="underline font-bold text-[#2675BE]"
                href="https://medium.com/@clout-fi/faq-1fb0e283f5f8"
                target="_blank"
              >
                こちら
              </Link>
              。
            </span>
            <div className="flex md:flex-row flex-col justify-between md:space-x-[24px] w-full">
              <SelectLabel
                initialValue="TWITTER"
                name={['requireTask', 'platForm']}
                options={[{ label: ' X (twitter)', value: 'TWITTER' }]}
              />
              <SelectLabel
                initialValue="twitter_follow"
                name={['requireTask', 'type']}
                options={[
                  {
                    value: 'twitter_follow',
                    label: 'フォローさせる',
                  },
                ]}
              />
            </div>
            <div className="w-full ">
              <div className="text-[14px] font-semibold mb-[5px]">ユーザーネーム</div>
              <Form.Item initialValue="@clout_fi" name={['requireTask', 'userFollow']} noStyle>
                <BasicInput disabled />
              </Form.Item>
            </div>
          </div>
        </Form>
      </div>
      <div className="flex space-x-[24px] pt-[48px] justify-center">
        <div className="w-[135px]  h-[56px]">
          <CButtonShadow
            classBgColor="bg-white"
            classRounded="rounded-[6px]"
            classShadowColor="bg-main-text"
            onClick={() => prevTab?.()}
            shadowSize="normal"
            textClass="md:text-[16px] text-[12px] bg-main-text"
            title="戻る"
            withIcon={{
              position: 'left',
              icon: (
                <svg fill="none" height="18" viewBox="0 0 18 18" width="18" xmlns="http://www.w3.org/2000/svg">
                  <path
                    d="M10.0469 14.7422L9.35938 15.4297C9.04688 15.7109 8.57812 15.7109 8.29688 15.4297L2.23438 9.33594C1.92188 9.05469 1.92188 8.58594 2.23438 8.30469L8.29688 2.21094C8.57812 1.92969 9.07812 1.92969 9.35938 2.21094L10.0469 2.89844C10.3594 3.21094 10.3281 3.67969 10.0469 3.99219L6.26562 7.55469H15.2656C15.6719 7.55469 16.0156 7.89844 16.0156 8.30469V9.30469C16.0156 9.74219 15.6719 10.0547 15.2656 10.0547H6.26562L10.0469 13.6484C10.3281 13.9609 10.3594 14.4297 10.0469 14.7422Z"
                    fill="#333333"
                  />
                </svg>
              ),
            }}
          />
        </div>
        <div className="w-[233px]  h-[56px]">
          <CButtonShadow
            classBgColor="bg-main-text"
            classRounded="rounded-[6px]"
            classShadowColor="bg-white"
            onClick={() => {
              if (hasTaskRequired) {
                form.submit();
              } else {
                toastMessage('必須タスクを一つ以上設定してください', 'error');
              }
            }}
            shadowSize="normal"
            textClass="md:text-[16px] text-[12px] text-white"
            title="保存して次へ進む"
            withIcon={{
              position: 'right',
              icon: (
                <svg fill="none" height="18" viewBox="0 0 18 18" width="18" xmlns="http://www.w3.org/2000/svg">
                  <path
                    d="M7.9375 2.89844L8.625 2.21094C8.9375 1.92969 9.40625 1.92969 9.6875 2.21094L15.7812 8.27344C16.0625 8.58594 16.0625 9.05469 15.7812 9.33594L9.6875 15.4297C9.40625 15.7109 8.9375 15.7109 8.625 15.4297L7.9375 14.7422C7.65625 14.4297 7.65625 13.9609 7.9375 13.6484L11.7188 10.0547H2.75C2.3125 10.0547 2 9.74219 2 9.30469V8.30469C2 7.89844 2.3125 7.55469 2.75 7.55469H11.7188L7.9375 3.99219C7.65625 3.67969 7.625 3.21094 7.9375 2.89844Z"
                    fill="white"
                  />
                </svg>
              ),
            }}
          />
        </div>
      </div>
    </>
  );
}

export default Task;
