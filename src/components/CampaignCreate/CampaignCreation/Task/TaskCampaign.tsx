/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable no-nested-ternary */
/* eslint-disable import/no-cycle */
/* eslint-disable max-lines-per-function */
// import InputLabel from '@/components/common/BasicInput/InputLabel';
import SelectLabel from '@/components/common/BasicSelect/SelectLabel';
import { Form, Image, Tooltip } from 'antd';
import React, { useEffect, useMemo } from 'react';
import { useRouter } from 'next/router';
import BasicInput from '@/components/common/BasicInput';
import BasicTextArea from '@/components/common/BasicTextArea';
import { renderDataPlatform } from '@/utils/renderDataPlatform';
import FlagItem from '@/components/common/FlagItem';
// import BasicSwitch from '@/components/common/BasicSwitch';
import BasicSwitch from '@/components/common/BasicSwitch';
import { TaskType } from '@/types/campaign.type';
import { TypeTasks } from './type';
import TaskQuestionCustom from './TaskQuestionCostom';

interface Props {
  item: TypeTasks;
  // id: number;
  onDelete: () => void;
  showDelete: boolean;
  index: number;
}
export interface DataPlatFormType {
  value?: string;
  label: string | null;
  content?: {
    id: number;
    title: string;
    type: string;
    require?: boolean;
    name: string;
    value?: string;
    isUrl?: boolean;
  }[];
}
const TaskCampaign = ({ item, onDelete, showDelete, index }: Props) => {
  const router = useRouter();
  const form = Form.useFormInstance();
  const platFormWatch = Form.useWatch(['optionTasks', `task${item.id}`, 'platForm'], form);
  const optionTasksForm = Form.useWatch(['optionTasks'], form);
  const typeTasksWatch = Form.useWatch(['optionTasks', `task${item.id}`, 'type'], form);
  const isEditPage = useMemo(() => router.pathname.startsWith('/campaign-creator/edit/'), [router.pathname]);
  const dataPlatForm = useMemo<DataPlatFormType[] | undefined>(
    () => renderDataPlatform(platFormWatch, item.config?.taskTemplate.config),
    [platFormWatch, item, item.config?.taskTemplate.config]
  );

  const hasTaskShareUrl = useMemo(() => {
    try {
      const listTask: TaskType[] = Object.values(optionTasksForm);
      if (listTask) {
        const taskRequired = listTask.find((taskItem) => taskItem.platForm === 'SHARE_URL');
        if (taskRequired) return true;
      }
      return false;
    } catch (error) {
      return false;
    }
  }, [optionTasksForm]);

  useEffect(() => {
    if (item.platForm && item.config) {
      form.setFieldValue(['optionTasks', `task${item.id}`, 'type'], item.platForm.type);
      form.setFieldValue(['optionTasks', `task${item.id}`, 'platForm'], item.platForm.name);
      // form.setFieldValue(['optionTasks', `task${item.id}`, 'taskId'], item.config.taskTemplateId);
      form.setFieldValue(['optionTasks', `task${item.id}`, 'taskId'], item.config.id);
      form.setFieldValue(['optionTasks', `task${item.id}`, 'points'], item.config?.taskTemplate?.points);
      form.setFieldValue(['optionTasks', `task${item.id}`, 'required'], item?.config?.taskTemplate?.required);
    }
  }, [item.platForm, item.config, item.id]);

  useEffect(() => {
    dataPlatForm
      ?.find((e) => e.value === typeTasksWatch)
      ?.content?.forEach((v) => {
        form.setFieldValue(['optionTasks', `task${item.id}`, `${v.name}`], v.value);
      });
  }, [dataPlatForm, typeTasksWatch, platFormWatch]);

  useEffect(() => {
    if (platFormWatch === 'SHARE_URL') {
      form.setFieldValue(['optionTasks', `task${item.id}`, 'required'], false);
    }
  }, [platFormWatch]);

  const isValidTwitterUrl = (_, value) => {
    const regex = /^(https?:\/\/)?(www\.)?(twitter\.com|x\.com)\/([a-zA-Z0-9_]+)(\/status\/([0-9]+))?\/?$/;
    const regexResult = regex.test(value);
    if (regexResult) {
      return Promise.resolve();
    }
    return Promise.reject(new Error('XのURLを入力してください'));
  };
  return (
    <>
      <Form.Item className="hidden" name={['optionTasks', `task${item.id}`, 'taskId']}>
        <FlagItem />
      </Form.Item>
      <div className="flex items-end justify-between text-[16px] font-semibold mb-[20px]">
        {item.title ? <span>{item.title}</span> : <span>タスク_{index + 1}</span>}
        {showDelete && (
          <Tooltip
            placement="bottomRight"
            title={isEditPage && !item?.isNewTask ? '既に設定されているタスクは削除できません' : ''}
          >
            <Image
              alt=""
              className={
                (isEditPage && !item?.isNewTask) || index === 0 ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'
              }
              onClick={() => {
                if ((isEditPage && !item?.isNewTask) || index === 0) return;
                onDelete();
              }}
              preview={false}
              src="/icons/icon-x-circle_new.svg"
            />
          </Tooltip>
        )}
      </div>
      <div className="md:p-[32px] md:pb-[20px] p-[22px] pb-0 rounded-[8px] border-2 border-[#333]">
        <div className="flex flex-col md:flex-row justify-between md:space-x-[24px]  w-full">
          <Tooltip placement="bottomRight" title={isEditPage && !item?.isNewTask ? 'タスクの種類は変更できません' : ''}>
            <SelectLabel
              disabled={isEditPage && !item?.isNewTask}
              initialValue="TWITTER"
              name={['optionTasks', `task${item.id}`, 'platForm']}
              options={[
                { label: ' X (twitter)', value: 'TWITTER' },
                { label: ' Webサイトを訪問させる', value: 'VISIT_PAGE' },
                { label: ' LINE友達登録させる', value: 'LINE' },
                { label: ' TikTok', value: 'TIKTOK' },
                { label: ' Telegram', value: 'TELEGRAM' },
                { label: ' Instagram', value: 'INSTAGRAM' },
                { label: ' Discord', value: 'DISCORD' },
                { label: ' 自由形式で質問する', value: 'CUSTOM' },
                { label: 'URLから友達を招待する', value: 'SHARE_URL', disabled: hasTaskShareUrl },
              ]}
            />
          </Tooltip>
          {dataPlatForm?.[0]?.value && platFormWatch !== 'CUSTOM' ? (
            <Tooltip placement="bottomRight" title={isEditPage ? 'タスクの種類は変更できません' : ''}>
              <SelectLabel
                disabled={isEditPage && !item?.isNewTask}
                initialValue={dataPlatForm?.[0]?.value}
                name={['optionTasks', `task${item.id}`, 'type']}
                options={dataPlatForm}
              />
            </Tooltip>
          ) : (
            <div className="w-full" />
          )}
        </div>
        <div className="flex flex-col space-y-[24px]">
          {dataPlatForm
            ?.find((e) => e.value === typeTasksWatch)
            ?.content?.map(
              (e) =>
                (e.type === 'input' && (
                  <div className="w-full" key={e.id}>
                    <div className="text-[14px] font-semibold mb-[5px] space-x-[8px]">
                      <span>{e.title}</span>
                      {/* {e.require && <span>※必須</span>} */}
                    </div>

                    <Form.Item
                      initialValue={e.value}
                      name={['optionTasks', `task${item.id}`, `${e.name}`]}
                      rules={
                        e.isUrl
                          ? [
                              { required: true, message: `${e.title}を入力してください。` },
                              typeTasksWatch === 'twitter_repost' || typeTasksWatch === 'twitter_repost_quote'
                                ? {
                                    validator: isValidTwitterUrl,
                                  }
                                : {
                                    type: 'url',
                                    message: '正しい URL 形式を入力してください。',
                                  },
                            ]
                          : (e as any)?.customRule &&
                              Array.isArray((e as any)?.customRule) &&
                              (e as any)?.customRule?.length
                            ? [...(e as any).customRule]
                            : [{ required: true, message: `${e.title}を入力してください。` }]
                      }
                    >
                      <BasicInput />
                    </Form.Item>
                  </div>
                )) ||
                (e.type === 'textArea' && (
                  <div className="w-full" key={e.id}>
                    <div className="text-[14px] font-semibold mb-[5px] space-x-[8px]">
                      <span>{e.title}</span>
                      {/* {e.require && <span>※必須</span>} */}
                    </div>
                    {e.name === 'quotePost' && (
                      <div className="text-[14px] font-semibold mb-[5px]">
                        テキストやハッシュタグ、リンクなどを含めることができます。未記入の場合、指定する文章はありません。
                      </div>
                    )}
                    <Form.Item
                      className="!mb-0"
                      initialValue={e.value}
                      name={['optionTasks', `task${item.id}`, `${e.name}`]}
                      rules={[{ required: true, message: `${e.title}を入力してください。` }]}
                    >
                      <BasicTextArea style={{ height: 145, resize: 'none' }} />
                    </Form.Item>
                  </div>
                ))
            )}
          {/* TASK QUESTION */}
          {platFormWatch === 'CUSTOM' && <TaskQuestionCustom dataPlatForm={dataPlatForm ?? []} item={item} />}
        </div>

        {/* <div className="w-full">
          <div className="text-[14px] font-semibold mb-[5px] space-x-[8px]">付与ポイント</div>
          <Form.Item className="" initialValue="1" name={['optionTasks', `task${item.id}`, 'points']}>
            <BasicInput disabled={isPublicCampaign && !item?.isNewTask} type="currency" />
          </Form.Item>
        </div> */}
        {platFormWatch === 'SHARE_URL' && (
          <div className="relative bottom-4 text-[12px] mt-4 font-semibold">
            ※このタスクは『手動で参加者に報酬を案内する』キャンペーンにのみ設定可能で、必須タスクに設定できません。
          </div>
        )}
        <div className="w-full flex items-center justify-end space-x-3 mb-6 md:mb-0">
          <span>必須タスクにする</span>
          <Tooltip
            placement="top"
            title={
              !item?.isNewTask && isEditPage
                ? '必須/任意は公開後に変更不可'
                : platFormWatch === 'SHARE_URL'
                  ? '必須タスクに設定できません'
                  : ''
            }
          >
            <div className="">
              <Form.Item name={['optionTasks', `task${item.id}`, 'required']} noStyle>
                <BasicSwitch disabled={platFormWatch === 'SHARE_URL' || (!item?.isNewTask && isEditPage)} />
              </Form.Item>
            </div>
          </Tooltip>
        </div>
      </div>
    </>
  );
};
export default TaskCampaign;
