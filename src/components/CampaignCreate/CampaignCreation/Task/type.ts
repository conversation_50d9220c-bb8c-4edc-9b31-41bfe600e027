export interface TypeTasks {
  id: number;
  title?: string;
  require?: boolean;
  platForm: {
    name: string;
    type: string;
  };
  config?: {
    id: number;
    campaignId: string;
    type: string;
    taskActionType: string;
    taskTemplateId: number;
    updatedAt: string;
    createdAt: string;
    taskTemplate: {
      id: number;
      userName: string;
      extra: string | null;
      config: TypeConfig;
      link: string;
      quote: string | null;
      required: boolean;
      points?: number;
      updatedAt: string;
      createdAt: string;
    };
  };
  required?: boolean;
  points?: number;
  isNewTask?: boolean;
}
export type TypeConfig = (
  | {
      type: 'formatSingle' | 'formatMultiple' | 'freeAnswer';
      title: string;
      listChoice?: { listChoice1: string };
      description: string;
      questionText: string;
      platForm: 'CUSTOM';
    }
  | {
      platForm: 'VISIT_PAGE';
      url: string;
      type: string;
    }
  | {
      platForm: 'SHARE_URL';
      type: string;
    }
  | {
      platForm: 'LINE';
      url: string;
      type: string;
    }
  | {
      platForm: 'TIKTOK';
      type: 'letThemWatch';
      linkWatch: string;
    }
  | {
      platForm: 'TIKTOK';
      type: 'letYouLike';
      linkToLike: string;
    }
  | {
      platForm: 'TIKTOK';
      type: 'letYouComment';
      linkToComment: string;
    }
  | {
      platForm: 'TIKTOK';
      type: 'letYouShare';
      linkToShare: string;
    }
  | {
      platForm: 'TIKTOK';
      type: 'makeYouFollow';
      userFollow: string;
    }
  | {
      platForm: 'INSTAGRAM';
      type: 'letYouLike';
      linkToLike: string;
    }
  | {
      platForm: 'INSTAGRAM';
      type: 'letYouComment';
      linkToComment: string;
    }
  | {
      platForm: 'INSTAGRAM';
      type: 'letYouShare';
      linkToShare: string;
    }
  | {
      platForm: 'INSTAGRAM';
      type: 'letYouMention';
      linkToMention: string;
    }
  | {
      platForm: 'INSTAGRAM';
      type: 'makeYouFollow';
      linkToFollow: string;
    }
  | {
      platForm: 'TELEGRAM';
      type: 'joinChannel';
      linkChannel: string;
    }
  | {
      platForm: 'TELEGRAM';
      type: 'viewPost';
      linkPost: string;
    }
  | {
      platForm: 'DISCORD';
      type: 'discord_invite';
      inviteLink: string;
    }
  | {
      platForm: 'TWITTER';
      type: 'twitter_follow';
      userFollow: string;
    }
  | {
      platForm: 'TWITTER';
      type: 'twitter_repost';
      postURL: string;
    }
  | {
      platForm: 'TWITTER';
      type: 'twitter_repost_quote';
      quotePost: string;
      postURLQuote: string;
    }
  | {
      platForm: 'TWITTER';
      type: 'twitter_make_post_with_hashtags';
      taskTitle: string;
      taskDescription: string;
      defaultPostText: string;
    }
  | {
      platForm: 'TWITTER';
      type: 'twitter_make_post';
      designatedClassicalChinese: string;
    }
) & { requireTask: boolean };
