@import '../../../../styles/globals.scss';
.customeTable {
  :global {
    .ant-table-wrapper .ant-table {
      border-radius: 6px;
    }
    .ant-table-thead .ant-table-cell {
      background-color: transparent;
    }
    .ant-table-wrapper .ant-table-container table > thead > tr:first-child > *:last-child {
      border-start-end-radius: 6px !important;
      border-start-end-radius: 6px !important;
    }
    .ant-table-wrapper .ant-table-container table > thead > tr:first-child > *:first-child {
      border-start-start-radius: 6px !important;
    }
    .ant-table-wrapper
      .ant-table.ant-table-bordered
      > .ant-table-container
      > .ant-table-content
      > table
      > tbody
      > tr
      > td {
      border-right: 1px solid #777;
      padding: 13px 40px;
      @include mobile {
        padding: 20px 0px;
      }

      &:last-child {
        border: none;
      }
    }
    // .ant-table-wrapper .ant-table.ant-table-bordered {
    //   border-inline-end: 1px solid #cbd2e0;
    // }
    .ant-table-wrapper
      .ant-table.ant-table-bordered
      > .ant-table-container
      > .ant-table-content
      > table
      > thead
      > tr
      > th {
      border-inline-end: 1px solid #fff;
      background: #777;
      font-size: 16px;
      font-weight: bold;
      color: white;
      padding: 13px 40px;
      @include mobile {
        padding: 20px 0px;
      }
    }

    .ant-table-wrapper .ant-table-container {
      border-start-start-radius: 0px !important;
      border-start-end-radius: 0px !important;
    }
    .ant-table-wrapper .ant-table-tbody > tr > td {
      border-bottom: none;
    }
    tr {
      &:nth-child(2n) {
        background-color: #f6f6f6;
      }
    }
    // :where(.css-dev-only-do-not-override-1adbn6x).ant-table-wrapper .ant-table.ant-table-bordered >.ant-table-container
    .ant-table-wrapper .ant-table.ant-table-bordered > .ant-table-container {
      // border-inline-start: 0px;
      border: 0px solid #cbd2e0;
    }

    @media screen and (max-width: 767.999px) {
      th.ant-table-cell {
        font-size: 13px !important;
      }
    }
  }
}
