import CButtonShadow from '@/components/common/CButtonShadow';

import React from 'react';

export default function SaveWarningPopup({
  email,
  onConfirm,
  onCancel,
}: {
  email: string;
  onConfirm: () => Promise<void>;
  onCancel: () => void;
}) {
  return (
    <div className="px-[16px] py-[32px] md:p-[64px]">
      <h1 className="font-bold text-[14px] md:text-[20px] text-[#04afaf] text-center">
        あなたが編集中に <br />
        このキャンペーンは{email ?? ''}が更新しました
      </h1>
      <div className="h-[32px]" />
      <p className="text-[12px] font-bold text-center">{email ?? ''}の更新を破棄して編集を上書きしますか？</p>
      <div className="h-[18px]" />
      <div className="h-[1px] bg-[#aaaaaa]" />
      <div className="h-[40px]" />
      <div className="w-[201px] h-[56px] mx-auto">
        <CButtonShadow
          classBgColor="bg-[#333]"
          classRounded="rounded-[6px]"
          classShadowColor="bg-white"
          onClick={async () => {
            await onConfirm();
          }}
          shadowSize="normal"
          textClass="text-white text-[16px] font-notoSans"
          title="上書き保存"
          type="button"
        />
      </div>
      <div className="h-[16px]" />
      <div className="text-center  ">
        <span
          aria-hidden
          className="text-center text-[12px] font-medium pb-[3px] border-b-[1px] border-b-[#333] hover:text-[#04afaf] hover:border-b-[#04afaf] cursor-pointer"
          onClick={() => {
            onCancel();
          }}
        >
          編集内容のキャンセル
        </span>
      </div>
    </div>
  );
}
