/* eslint-disable import/no-cycle */
import BasicInput from '@/components/common/BasicInput';
import { Form } from 'antd';
import { useEffect } from 'react';
// import BasicSwitch from '@/components/common/BasicSwitch';
import Link from 'next/link';
import { TaskUpdate } from '.';

export const renderWithDataTask = (data: TaskUpdate) => {
  let title: string = '';
  let description: string = '';
  let isLink: boolean = false;

  switch (data.taskTemplate.config.platForm) {
    case 'TWITTER':
      switch (data.taskTemplate.config.type) {
        case 'twitter_follow': {
          title = 'X(Twitter)でフォローする';
          description = data.taskTemplate.config.userFollow;
          break;
        }
        case 'twitter_repost': {
          title = 'X(Twitter)でリツイートする';
          description = data.taskTemplate.config.postURL;
          isLink = true;
          break;
        }
        case 'twitter_repost_quote': {
          title = 'X(Twitter)で引用リツイートする';
          description = data.taskTemplate.config.postURLQuote;
          isLink = true;
          break;
        }
        case 'twitter_make_post_with_hashtags': {
          title = 'X(Twitter)で指定ハッシュタグ付きの投稿をする';
          description = data.taskTemplate.config.taskTitle;
          break;
        }
        case 'twitter_make_post': {
          title = 'X(Twitter)で指定文言を投稿する';
          description = data.taskTemplate.config.designatedClassicalChinese;
          break;
        }
        default:
          return null;
      }
      break;
    case 'VISIT_PAGE': {
      title = 'Webサイトを訪問する';
      description = data.taskTemplate.config.url;
      isLink = true;
      break;
    }
    case 'LINE': {
      title = 'LINEで友だち登録をする';
      description = data.taskTemplate.config.url;
      isLink = true;
      break;
    }
    case 'TIKTOK':
      switch (data.taskTemplate.config.type) {
        case 'letThemWatch': {
          title = 'TikTokで視聴する';
          description = data.taskTemplate.config.linkWatch;
          isLink = true;
          break;
        }
        case 'letYouLike': {
          title = 'いいねする';
          description = data.taskTemplate.config.linkToLike;
          isLink = true;
          break;
        }
        case 'letYouComment': {
          title = 'コメントする';
          description = data.taskTemplate.config.linkToComment;
          isLink = true;
          break;
        }
        case 'letYouShare': {
          title = '再投稿させる';
          description = data.taskTemplate.config.linkToShare;
          isLink = true;
          break;
        }
        case 'makeYouFollow': {
          title = 'TikTokでフォローする';
          description = data.taskTemplate.config.userFollow;
          break;
        }
        default:
          return null;
      }
      break;
    case 'INSTAGRAM':
      switch (data.taskTemplate.config.type) {
        case 'letYouLike': {
          title = 'いいねする';
          description = data.taskTemplate.config.linkToLike;
          isLink = true;
          break;
        }
        case 'letYouComment': {
          title = 'コメントする';
          description = data.taskTemplate.config.linkToComment;
          isLink = true;
          break;
        }
        case 'letYouShare': {
          title = 'シェアする';
          description = data.taskTemplate.config.linkToShare;
          isLink = true;
          break;
        }
        case 'letYouMention': {
          title = 'ストーリーでメンションする';
          description = data.taskTemplate.config.linkToMention;
          // isLink = true;
          break;
        }
        case 'makeYouFollow': {
          title = 'フォローする';
          description = data.taskTemplate.config.linkToFollow;
          // isLink = true;
          break;
        }
        default:
          return null;
      }
      break;
    case 'TELEGRAM':
      switch (data.taskTemplate.config.type) {
        case 'joinChannel': {
          title = 'Telegramでチャンネルに参加する';
          description = data.taskTemplate.config.linkChannel;
          isLink = true;
          break;
        }
        case 'viewPost': {
          title = 'Telegramで投稿を閲覧する';
          description = data.taskTemplate.config.linkPost;
          isLink = true;
          break;
        }
        default:
          return null;
      }
      break;
    case 'DISCORD': {
      title = 'DiscordでサーバーにJoinする';
      description = data.taskTemplate.config.inviteLink;
      isLink = true;
      break;
    }
    case 'CUSTOM': {
      description = data.taskTemplate.config.questionText;
      switch (data.taskTemplate.config.type) {
        case 'freeAnswer': {
          title = 'アンケートに回答する';
          break;
        }
        case 'formatMultiple': {
          title = 'アンケートに回答する';
          break;
        }
        case 'formatSingle': {
          title = 'アンケートに回答する';
          break;
        }
        default:
          return null;
      }
      break;
    }
    case 'SHARE_URL': {
      description = '';
      title = 'URLで友達を招待する';
      break;
    }
    default:
      return null;
  }
  return { title: `${title} ${data?.taskTemplate.required ? '(必須)' : ''}`, description, isLink };
};

function ListSetPointTask({
  itemsData,
  typeWinnerWatch,
}: {
  itemsData: TaskUpdate[];
  typeWinnerWatch: string | undefined;
}) {
  const form = Form.useFormInstance();
  // const requiredShareUrl = Form.useWatch(['taskShareUrl', 'required'], form);
  useEffect(() => {
    itemsData.forEach((item) => {
      if (item.type === 'SHARE_URL') {
        form.setFieldValue(
          ['taskShareUrl', 'points'],
          item?.taskTemplate?.points && item?.taskTemplate?.points > 0 ? item?.taskTemplate?.points : 1
        );
      } else {
        form.setFieldValue(
          ['newPoints', `${item.taskId}`, 'points'],
          item?.taskTemplate?.points && item?.taskTemplate?.points > 0 ? item?.taskTemplate?.points : 1
        );
      }
    });

    const itemShareURl = itemsData.find((item) => item.type === 'SHARE_URL');
    if (itemShareURl) {
      form.setFieldValue(['taskShareUrl', 'required'], true);
    }
  }, [itemsData]);
  if (typeWinnerWatch !== 'MANUAL_SELECTION') {
    return <div />;
  }

  const rulePoint = (_, value) => {
    const regex = /^[1-9][0-9]?$/;
    const regexResult = regex.test(value);
    if (value && regexResult) {
      return Promise.resolve();
    }
    return Promise.reject(new Error('1〜100の整数を入力してください。'));
  };

  return (
    <div className="">
      {itemsData && itemsData.length > 0 && (
        <>
          <div className="mb-2 font-bold">付与ポイント ※必須</div>
          {itemsData.map((item, index) => (
            <div
              className="w-full p-10 border border-[2px] rounded-[6px] border-[#333] mb-6"
              key={`itemDatas_${item.taskId}`}
            >
              <div className="text-[14px] font-semibold mb-[5px] space-x-[8px] text-[#2D3648]">タスク_{index + 1}</div>
              <div className="text-[14px] font-semibold mb-[5px] space-x-[8px] text-[#2D3648]">
                {renderWithDataTask(item)?.title}
              </div>
              {renderWithDataTask(item)?.isLink ? (
                <Link
                  className="font-bold tracking-[0.6px] text-[#04AFAF]"
                  href={renderWithDataTask(item)?.description || ''}
                  target="_blank"
                >
                  <p className="mb-[16px] break-all text-ellipsis">{renderWithDataTask(item)?.description}</p>
                </Link>
              ) : (
                <div className="text-[14px] font-semibold mb-[16px] space-x-[8px] text-[#2D3648]">
                  {renderWithDataTask(item)?.description}
                </div>
              )}

              <div className="text-[14px] font-semibold mb-[5px] space-x-[8px] text-[#2D3648]">付与ポイント</div>
              <Form.Item
                className=""
                initialValue={1}
                name={['newPoints', `${item.taskId}`, 'points']}
                rules={[{ validator: rulePoint }]}
              >
                <BasicInput className="!border-[#CBD2E0] !h-[48px]" type="currency" />
              </Form.Item>
            </div>
          ))}
        </>
      )}
      {/* <div className="mb-2 flex">
        <span className="mr-6 font-bold">URLから友達を招待するタスクを追加</span>
        <Form.Item name={['taskShareUrl', 'required']} noStyle>
          <BasicSwitch />
        </Form.Item>
      </div>
      <div className="w-full p-10 border border-[2px] rounded-[6px] border-[#333] mb-6">
        <div className="text-[14px] font-semibold mb-[5px] space-x-[8px] text-[#2D3648]">付与ポイント</div>
        <Form.Item
          className="mb-2"
          initialValue={1}
          name={['taskShareUrl', 'points']}
          rules={[{ validator: rulePoint }]}
        >
          <BasicInput className="!border-[#CBD2E0] !h-[48px]" disabled={!requiredShareUrl} type="currency" />
        </Form.Item>
        <p className="text-[14px]">× 招待人数</p>
      </div> */}
    </div>
  );
}

export default ListSetPointTask;
