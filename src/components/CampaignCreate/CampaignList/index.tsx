/* eslint-disable import/no-extraneous-dependencies */
import BasicTabs from '@/components/common/BasicTabs';
import CButtonShadow from '@/components/common/CButtonShadow';
import FileIcon from '@/components/common/icons/FileIcon';
import { RootState } from '@/redux/store';
import type { TabsProps } from 'antd';
import { useRouter } from 'next/router';
import { useSelector } from 'react-redux';
import BasicInput from '@/components/common/BasicInput';
import { useState } from 'react';
import { useDebounce } from '@uidotdev/usehooks';
import TableAll from './TableAll';

const ListTab = ({ title }: { title: string }) => (
  <div className=" flex items-center space-x-[8px]">
    {/* <Image alt="" preview={false} src="/icons/icon-placeholder.svg" /> */}
    <span className="text-[16px] font-bold">{title}</span>
  </div>
);
function CampaignList() {
  const router = useRouter();
  const { user } = useSelector((state: RootState) => state.auth);
  const [text, setText] = useState<string>('');
  const paramSearch = useDebounce(text, 500);
  const items: TabsProps['items'] = [
    {
      key: '1',
      label: <ListTab title="すべて" />,
      children: <TableAll paramSearch={paramSearch} status="ALL" />,
    },
    {
      key: '2',
      label: <ListTab title="下書き・購入待ち" />,
      children: <TableAll paramSearch={paramSearch} status="DRAFT" />,
    },
    // {
    //   key: '3',
    //   label: <ListTab title="審査中" />,
    //   children: <TableAll status="UNDER_REVIEW" />,
    // },
    {
      key: '4',
      label: <ListTab title="公開待ち" />,
      children: <TableAll paramSearch={paramSearch} status="WAITING_FOR_PUBLICATION" />,
    },
    {
      key: '5',
      label: <ListTab title="公開中" />,
      children: <TableAll paramSearch={paramSearch} status="PUBLIC" />,
    },
    {
      key: '6',
      label: <ListTab title="完了" />,
      children: <TableAll paramSearch={paramSearch} status="COMPLETION" />,
    },
  ];

  return (
    <div className="xl:px-[48px] px-[20px] pb-[77px] w-full">
      <div className="flex md:flex-row flex-col py-[32px] w-full justify-between border-b-2 border-[#2D3648] md:max-h-[112px] md:space-y-0 space-y-[10px]">
        <span className="xl:text-[32px] text-[24px] font-bold">キャンペーン一覧</span>
        {user?.isSuperAdmin !== true && (
          <div className="md:w-[165px] w-[108px]  md:h-[56px] h-[46px]">
            <CButtonShadow
              classBgColor="bg-main-text"
              classRounded="rounded-[6px]"
              classShadowColor="bg-white"
              onClick={() => router.push('/campaign-creator/create')}
              shadowSize="normal"
              textClass="md:text-[16px] text-[14px] text-white"
              title="新規作成"
              withIcon={{ position: 'left', icon: <FileIcon color="#fff" /> }}
            />
          </div>
        )}
      </div>

      <div className="pt-[28px] md:max-w-[calc(100vw_-_304px)]">
        {user?.isSuperAdmin && (
          <div className="flex items-center mb-5">
            <p className="font-bold mr-2">組織</p>
            <div className="max-w-[200px]">
              <BasicInput
                className="!h-[40px] !min-h-[40px]"
                onChange={(e) => {
                  setText(e.target.value);
                }}
                placeholder="ID/Name"
              />
            </div>
          </div>
        )}
        <BasicTabs
          defaultActiveKey={router.query.type === 'public' ? '5' : '1'}
          items={items}
          onChange={() => router.replace({ query: {} })}
        />
      </div>
    </div>
  );
}

export default CampaignList;
