/* eslint-disable react/no-unstable-nested-components */
/* eslint-disable import/no-extraneous-dependencies */
import styles from '@/components/common/BasicTable/index.module.scss';
import ArrowDown from '@/components/common/icons/ArrowDown';
import CircleArrow from '@/components/common/icons/CircleArrow';
import useWindowDimensions from '@/hooks/useWindowDimensions';
import { useGetListCampaignQuery } from '@/redux/endpoints/campaign';
import { RootState } from '@/redux/store';
import { formatNumber } from '@/utils/formatNumber';
import { Table } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import moment from 'moment';
import { useRouter } from 'next/router';
import React, { useEffect, useMemo, useState } from 'react';
import { useSelector } from 'react-redux';

interface DataType {
  key: React.Key;
  campaignName: string;
  status: string;
  startDate: string;
  endDate: string;
  winner: string;
  campaignBalance: number;
}

function TableAll({
  status,
  paramSearch,
}: {
  status?: 'ALL' | 'DRAFT' | 'UNDER_REVIEW' | 'WAITING_FOR_PUBLICATION' | 'PUBLIC' | 'COMPLETION';
  paramSearch?: string;
}) {
  const { user } = useSelector((state: RootState) => state.auth);

  const { width } = useWindowDimensions();
  const [pageTable, setPageTable] = useState<number>(0);
  const { push, query, isReady } = useRouter();
  const {
    data: dataTable,
    isLoading,
    isFetching,
  } = useGetListCampaignQuery(
    {
      skip: pageTable ?? 0,
      take: 20,
      actionFrom: 'ADMIN',
      status: status === 'ALL' ? undefined : status,
      q: user?.isSuperAdmin && paramSearch !== '' ? paramSearch : undefined,
    },
    { refetchOnMountOrArgChange: true }
  );

  useEffect(() => {
    if (query.page) {
      setPageTable(Number(Number(query.page) - 1) * 20);
    }
    return () => {
      setPageTable(1);
    };
  }, [isReady, query?.page]);

  const columns: ColumnsType<DataType> = useMemo(() => {
    const columnDefault: ColumnsType<DataType> = [
      {
        title: 'キャンペーン名',
        dataIndex: 'campaignName',
        render: (text: string) => <span className="text-[#04AFAF] underline underline-offset-2">{text}</span>,
        sorter: {
          compare: (a, b) => a.campaignName.localeCompare(b.campaignName),
        },
        sortIcon: () => <ArrowDown />,
      },

      {
        title: 'ステータス',
        dataIndex: 'status',
        // sorter: {
        //   compare: (a, b) => a.status - b.status,
        // },
        render: (
          value: 'DRAFT' | 'WAITING_FOR_PURCASE' | 'UNDER_REVIEW' | 'WAITING_FOR_PUBLICATION' | 'PUBLIC' | 'COMPLETION'
        ) => {
          switch (value) {
            case 'DRAFT':
              return '下書き';
            case 'WAITING_FOR_PURCASE':
              return '購入待ち';
            case 'UNDER_REVIEW':
              return '審査中';
            case 'WAITING_FOR_PUBLICATION':
              return '公開待ち';
            case 'PUBLIC':
              return '公開中';
            case 'COMPLETION':
              return '完了';
            default:
              return '下書き';
          }
        },
        sortIcon: () => <ArrowDown />,
      },
      {
        title: '開始日時',
        dataIndex: 'startDate',
        render: (value) => (value ? moment(value ?? undefined).format('YYYY/MM/DD HH:mm') : '-'),
        sorter: {
          compare: (a, b) => moment(a.startDate).diff(moment(b.startDate)),
        },
        sortIcon: () => <ArrowDown />,
      },
      {
        title: '終了日時',
        dataIndex: 'endDate',
        render: (value) => (value ? moment(value).format('YYYY/MM/DD HH:mm') : '-'),
        sorter: {
          compare: (a, b) => moment(a?.endDate).diff(moment(b?.endDate)),
        },
        sortIcon: () => <ArrowDown />,
      },
      {
        title: '当選者選択方法',
        dataIndex: 'winner',
        sorter: {
          compare: (a, b) => a.winner.localeCompare(String(b)),
        },
        sortIcon: () => <ArrowDown />,
      },
      {
        title: 'キャンペーン予算',
        dataIndex: 'campaignBalance',
        render: (value) => (!value ? '-' : `¥${formatNumber(value, true, 1)}`),
        sorter: {
          compare: (a, b) => Number(a.campaignBalance) - Number(b.campaignBalance),
        },
        sortIcon: () => <ArrowDown />,
      },
    ];
    if (user?.isSuperAdmin) {
      columnDefault.unshift({
        title: '組織名',
        dataIndex: 'companyName',
      });
    }
    return columnDefault;
  }, [user?.isSuperAdmin]);

  const data = useMemo<DataType[] | undefined>(() => {
    if (dataTable) {
      let dataCampaign: DataType[] = [];
      dataCampaign = dataTable.campaigns.map((item) => ({
        key: item.id,
        campaignName: item.title,
        status:
          item.methodOfselectWinners === 'AUTO_PRIZEE_DRAW' &&
          item.status === 'DRAFT' &&
          item?.isWaitingPurcare === true
            ? 'WAITING_FOR_PURCASE'
            : item.status,
        startDate: item.startTime,
        endDate: item.expiredTime,
        winner: item.methodOfselectWinners === 'AUTO_PRIZEE_DRAW' ? 'インスタントウィン' : 'マニュアル',
        campaignBalance: item.totalPrizeValue,
        companyName: item.company?.name,
      }));
      if (status === 'ALL') {
        return dataCampaign;
      }
      return dataCampaign.filter((e) => {
        if (e.status === 'WAITING_FOR_PURCASE') {
          return status === 'DRAFT';
        }
        return e.status === status;
      });
    }
    return undefined;
  }, [dataTable?.campaigns, status]);

  return (
    <div className={styles.customTable}>
      <Table
        columns={columns}
        dataSource={data}
        loading={isLoading || isFetching}
        onRow={(record) => ({
          onClick: () => push(`/campaign-creator/list/${record.key}`),
        })}
        pagination={{
          position: ['bottomCenter'],
          pageSize: 20,
          total: dataTable?.total ?? 0,
          showSizeChanger: false,
          jumpNextIcon: <span className="text-[16px] font-medium tracking-[0.48px]">...</span>,
          jumpPrevIcon: <span className="text-[16px] font-medium tracking-[0.48px]">...</span>,
          prevIcon: <CircleArrow position="left" />,
          nextIcon: <CircleArrow />,
          simple: width < 600,
          // eslint-disable-next-line react/no-unstable-nested-components
          showTotal: width < 400 ? undefined : () => <span>{dataTable?.total ?? 0} 件</span>,
          onChange: (page) => {
            push({ query: { page } }, undefined, { shallow: true, scroll: true });
          },
          // current: pageTable,
        }}
        scroll={{ x: 1050 }}
        tableLayout="fixed"
      />
    </div>
  );
}
TableAll.defaultProps = {
  status: 'ALL',
  paramSearch: undefined,
};
export default TableAll;
