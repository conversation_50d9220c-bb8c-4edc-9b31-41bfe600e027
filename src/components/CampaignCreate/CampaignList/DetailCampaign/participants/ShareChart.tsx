/* eslint-disable react/jsx-no-bind */
/* eslint-disable react/no-unstable-nested-components */
/* eslint-disable react/prop-types */
import { useGetListCampaignUsersByChartQuery } from '@/redux/endpoints/campaign';
import { useRouter } from 'next/router';
import React, { useMemo, useState } from 'react';
import { CartesianGrid, Line, LineChart, ResponsiveContainer, Tooltip, XAxis, YAxis } from 'recharts';

export default function ShareChart() {
  const { query } = useRouter();
  const [totalUserJoinCampaign, setTotalUserJoinCampaign] = useState<number>(0);
  const [totalUserVisitCampaign, setTotalUserVisitCampaign] = useState<number>(0);
  const [dateType, setDateType] = useState<'week' | 'day'>('day');
  const { data: listDataCampaignUser } = useGetListCampaignUsersByChartQuery(
    {
      campaignId: String(query?.id),
      params: {
        skip: 0,
        action: 'chart',
        dateType,
      },
    },
    { refetchOnMountOrArgChange: true }
  );

  const dataChart = useMemo(() => {
    if (listDataCampaignUser?.data) {
      const dataExample = { ...listDataCampaignUser?.data };
      const totalUserJoin = Object.values(dataExample)?.[Object.keys(dataExample).length - 1]?.joinCampaignTotal;
      const totalUserVisit = Object.values(dataExample)?.[Object.keys(dataExample).length - 1]?.visitCampaignTotal;
      setTotalUserJoinCampaign(totalUserJoin);
      setTotalUserVisitCampaign(totalUserVisit);
      return Object.keys(dataExample).map((key) => ({
        name: key,
        join: listDataCampaignUser?.data[key].joinCampaignToday,
        visit: listDataCampaignUser?.data[key].visitCampaignToday,
      }));
    }
    return [];
  }, [listDataCampaignUser]);

  // eslint-disable-next-line react/no-unstable-nested-components, react/prop-types
  const CustomTooltip = ({ active, payload, label }) => {
    if (active && payload && payload.length) {
      return (
        <div className="custom-tooltip">
          <p className="intro">{label}</p>
          <p className="label">{`キャンペーン訪問人数:${totalUserVisitCampaign} (新規: ${payload[0].value}人) `}</p>
          <p className="label">{`キャンペーン応募人数:${totalUserJoinCampaign} (新規: ${payload[1].value}人) `}</p>
        </div>
      );
    }

    return null;
  };

  function IntegerTickFormatter(value) {
    // Kiểm tra và chuyển đổi an toàn giá trị thành số nguyên
    const numericValue = Number(value);
    // Chỉ áp dụng toFixed nếu numericValue là một số hợp lệ
    // eslint-disable-next-line no-restricted-globals, no-nested-ternary
    return isNaN(numericValue) ? value : numericValue < 1 && numericValue !== 0 ? '' : numericValue;
  }

  return (
    <div>
      <div className="flex justify-end mb-4">
        <button
          className={`w-[56px] h-[30px] flex justify-center items-center rounded-[16px] ${
            dateType === 'day' && 'font-bold bg-[#EDF0F7]'
          }`}
          onClick={() => setDateType('day')}
          type="button"
        >
          日
        </button>
        <button
          className={`w-[56px] h-[30px] flex justify-center items-center rounded-[16px] ${
            dateType === 'week' && 'font-bold bg-[#EDF0F7]'
          }`}
          onClick={() => setDateType('week')}
          type="button"
        >
          週
        </button>
      </div>
      <div className="w-full h-[400px]">
        <ResponsiveContainer height="100%" width="100%">
          <LineChart
            data={dataChart}
            height={300}
            margin={{
              top: 5,
              right: 30,
              left: -20,
              bottom: 5,
            }}
            width={500}
          >
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="name" />
            <YAxis tickFormatter={IntegerTickFormatter} />
            <Tooltip
              content={({ active, payload, label }) => (
                <CustomTooltip active={active} label={label} payload={payload} />
              )}
            />
            <Line dataKey="visit" stroke="#A5A5A5" type="monotone" />
            <Line dataKey="join" stroke="#2D3648" type="monotone" />
          </LineChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
}
