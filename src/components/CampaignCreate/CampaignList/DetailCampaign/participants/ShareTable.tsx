import React, { useMemo, useState } from 'react';
import styles from '@/components/common/BasicTable/index.module.scss';
import CircleArrow from '@/components/common/icons/CircleArrow';
import { Table } from 'antd';
import { ColumnsType } from 'antd/es/table';
import { useGetStatisticCampaignUsersQuery } from '@/redux/endpoints/campaign';
import { useRouter } from 'next/router';
import useWindowDimensions from '@/hooks/useWindowDimensions';
import Link from 'next/link';

interface DataType {
  accountName: string;
  count1: number;
  count2: number;
}

const PAGE_SIZE = 10;

export default function ShareTable() {
  const { query } = useRouter();
  const { width } = useWindowDimensions();
  const [pageTable, setPageTable] = useState<number>(1);
  const { data: listDataCampaignUser } = useGetStatisticCampaignUsersQuery(
    {
      campaignId: String(query?.id),
      params: {
        action: 'list',
        skip: pageTable ? (pageTable - 1) * PAGE_SIZE : 0,
        take: PAGE_SIZE,
      },
    },
    { refetchOnMountOrArgChange: true }
  );

  const columns = useMemo<ColumnsType<DataType>>(
    () => [
      {
        title: 'キャンペーン紹介者',
        dataIndex: 'accountName',
        render: (text: string) => (
          <Link
            className="text-[#04AFAF] underline underline-offset-2 cursor-pointer"
            href={`https://x.com/${text}`}
            target="_blank"
          >
            {text}
          </Link>
        ),
      },
      {
        title: 'キャンペーン訪問人数(人)',
        dataIndex: 'count1',
      },
      {
        title: 'キャンペーン応募人数(人)',
        dataIndex: 'count2',
        sorter: (a, b) => a.count2 - b.count2,
      },
    ],
    []
  );

  const dataTable = useMemo(() => {
    if (listDataCampaignUser?.data) {
      const listUsers = listDataCampaignUser.data.map((item) => ({
        accountName: item.identityAccountName,
        count1: item.sharedUrlsWithLinkVisitsCount,
        count2: item.sharedUrlsWithTasks,
      }));
      return listUsers;
    }
    return [];
  }, [listDataCampaignUser]);

  return (
    <div className={styles.customTable}>
      <Table
        columns={columns}
        dataSource={dataTable}
        loading={false}
        // onRow={(record) => ({
        //   onClick: () => {
        //     push(`/campaign-creator/list/${record.key}`);
        //     // trigger({ campaignId: String(record.key) }); // click row
        //   },
        // })}
        pagination={{
          position: ['bottomCenter'],
          pageSize: PAGE_SIZE,
          total: listDataCampaignUser?.total ?? 0,
          showSizeChanger: false,
          jumpNextIcon: <span className="text-[16px] font-medium tracking-[0.48px]">...</span>,
          jumpPrevIcon: <span className="text-[16px] font-medium tracking-[0.48px]">...</span>,
          prevIcon: <CircleArrow position="left" />,
          nextIcon: <CircleArrow />,
          simple: width < 600,
          // eslint-disable-next-line react/no-unstable-nested-components
          showTotal: () => <span>{listDataCampaignUser?.total ?? 0} 件</span>,
          onChange: (page) => {
            setPageTable(page);
          },
        }}
        scroll={{ x: 900 }}
        tableLayout="fixed"
      />
    </div>
  );
}
