/* eslint-disable import/no-extraneous-dependencies */
import TableAll from '@/components/CampaignCreate/CampaignList/TableAll';
import BasicInput from '@/components/common/BasicInput';
import CButtonShadow from '@/components/common/CButtonShadow';
import FileIcon from '@/components/common/icons/FileIcon';
import { RootState } from '@/redux/store';
import { useDebounce } from '@uidotdev/usehooks';
import { useRouter } from 'next/router';
import React, { useState } from 'react';
import { useSelector } from 'react-redux';

function PageCompleted() {
  const { user } = useSelector((state: RootState) => state.auth);
  const [text, setText] = useState<string>('');
  const paramSearch = useDebounce(text, 500);
  const router = useRouter();
  return (
    <div className="xl:px-[48px] px-[20px] pb-[77px]">
      <div className="flex py-[32px] w-full justify-between border-b-2 border-[#2D3648] md:max-h-[112px] flex-col md:flex-row gap-[16px]">
        <span className="text-[24px] md:text-[32px] font-bold">キャンペーン一覧</span>
        {user?.isSuperAdmin !== true && (
          <div className="w-[165px]  h-[56px]">
            <CButtonShadow
              classBgColor="bg-main-text"
              classRounded="rounded-[6px]"
              classShadowColor="bg-white"
              onClick={() => router.push('/campaign-creator/create')}
              shadowSize="normal"
              title="新規作成"
              withIcon={{ position: 'left', icon: <FileIcon color="#fff" /> }}
            />
          </div>
        )}
      </div>

      <div className="pt-[28px]">
        {user?.isSuperAdmin && (
          <div className="flex items-center mb-5">
            <p className="font-bold mr-2">組織</p>
            <div className="max-w-[200px]">
              <BasicInput
                className="!h-[40px] !min-h-[40px]"
                onChange={(e) => {
                  setText(e.target.value);
                }}
                placeholder="ID/Name"
              />
            </div>
          </div>
        )}
        <TableAll paramSearch={paramSearch} status="COMPLETION" />
      </div>
    </div>
  );
}

export default PageCompleted;
