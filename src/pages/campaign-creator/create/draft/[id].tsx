/* eslint-disable guard-for-in */
/* eslint-disable no-restricted-syntax */
/* eslint-disable @typescript-eslint/no-explicit-any */
import Notfound from '@/components/404';
import { useCampaignApiContext } from '@/context/CampaignApiContext';
import { CampaignApi } from '@/redux/endpoints/campaign';
import { RootState, wrapper } from '@/redux/store';
import { useRouter } from 'next/router';
import { useSelector } from 'react-redux';
import dynamic from 'next/dynamic';

export const getServerSideProps = wrapper.getServerSideProps((store) => async ({ params }) => {
  const id = params?.id;

  const { data: dataCampaign } = await store.dispatch(
    CampaignApi.endpoints.getDetailCampaign.initiate({
      campaignId: id as string,
      token: 'user',
    })
  );
  if (dataCampaign?.status !== 'DRAFT') {
    return {
      notFound: true,
    };
  }
  return {
    props: {},
  };
});

const CampaignCreation = dynamic(() => import('@/components/CampaignCreate/CampaignCreation'), {
  ssr: false,
});

function DraftPage() {
  const { isValidUserLogged } = useCampaignApiContext();
  const { user } = useSelector((state: RootState) => state.auth);
  const router = useRouter();

  if (user?.isSuperAdmin === true) {
    router.replace('/campaign-creator');
    return <div />;
  }

  if (isValidUserLogged === true) {
    return <CampaignCreation />;
  }
  if (isValidUserLogged === false) {
    return <Notfound isShowBackBtn={false} />;
  }
  return <div />;
}

export default DraftPage;
