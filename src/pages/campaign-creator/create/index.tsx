import React from 'react';
import { useSelector } from 'react-redux';
import { RootState } from '@/redux/store';
import { ConditionPage } from '@/components/CampaignCreate/ConditionPage';
import CampaignCreation from '@/components/CampaignCreate/CampaignCreation';
import { useRouter } from 'next/router';

// const CampaignCreation = dynamic(() => import('@/components/CampaignCreate/CampaignCreation'), {
//   ssr: false,
//   loading: () => (
//     <div className="flex items-center justify-center h-full">
//       <Spin />
//     </div>
//   ),
// });
function CampaignCreatePage() {
  const router = useRouter();
  // const { push } = useRouter();
  const { user } = useSelector((state: RootState) => state.auth);
  if (user?.isSuperAdmin === true) {
    router.replace('/campaign-creator');
    return <div />;
  }
  if (user?.companyId == null) {
    return <ConditionPage />;
  }
  return <CampaignCreation />;
}

export default CampaignCreatePage;
