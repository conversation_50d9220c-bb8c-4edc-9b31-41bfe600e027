/* eslint-disable guard-for-in */
/* eslint-disable no-restricted-syntax */
/* eslint-disable @typescript-eslint/no-explicit-any */
import Notfound from '@/components/404';
import { useCampaignApiContext } from '@/context/CampaignApiContext';
import { CampaignApi } from '@/redux/endpoints/campaign';
import { wrapper } from '@/redux/store';
import dynamic from 'next/dynamic';

export const getServerSideProps = wrapper.getServerSideProps((store) => async ({ params }) => {
  const id = params?.id;

  const { data: dataCampaign } = await store.dispatch(
    CampaignApi.endpoints.getDetailCampaign.initiate({
      campaignId: id as string,
      token: 'user',
    })
  );
  if (
    dataCampaign?.status !== 'UNDER_REVIEW' &&
    dataCampaign?.status !== 'WAITING_FOR_PUBLICATION' &&
    dataCampaign?.status !== 'PUBLIC'
  ) {
    return {
      notFound: true,
    };
  }
  return {
    props: {},
  };
});

const CampaignCreation = dynamic(() => import('@/components/CampaignCreate/CampaignCreation'), {
  ssr: false,
});

function EditPage() {
  const { isValidUserLogged } = useCampaignApiContext();

  if (isValidUserLogged === true) {
    return <CampaignCreation />;
  }
  if (isValidUserLogged === false) {
    return <Notfound isShowBackBtn={false} />;
  }
  return <div />;
}

export default EditPage;
