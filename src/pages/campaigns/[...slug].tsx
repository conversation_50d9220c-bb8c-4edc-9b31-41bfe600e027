/* eslint-disable max-lines */
/* eslint-disable react/no-danger */
/* eslint-disable no-nested-ternary */
/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable no-console */
import CampaignDetail from '@/components/CampaignDetail';
import CampaignDetailProvider from '@/components/CampaignDetail/CampainContext';
import RecommedCampaignsSection from '@/components/CampaignDetail/RecommedCampaignsSection';
import GoogleAd from '@/components/GoogleAd';
import useWindowDimensions from '@/hooks/useWindowDimensions';
import { useMeQuery } from '@/redux/endpoints/auth';
import { bannerApi, TypeBannerResponse } from '@/redux/endpoints/banner';
import { CampaignApi, ListCampaignParams, TypeCampaign } from '@/redux/endpoints/campaign';
import { RootState, wrapper } from '@/redux/store';
import { useSelector } from 'react-redux';

export const getServerSideProps = wrapper.getServerSideProps((store) => async ({ params }) => {
  if (
    !Array.isArray(params?.slug) ||
    (Array.isArray(params?.slug) && params?.slug && params?.slug?.length > 2) ||
    (Array.isArray(params?.slug) &&
      params?.slug &&
      params?.slug?.length === 2 &&
      ['completion', 'winning', 'losing']?.find((i) => params?.slug?.[1] === i) === undefined)
  ) {
    return {
      notFound: true,
    };
  }

  const id = params?.slug?.[0] ? params?.slug?.[0] : '';
  console.log(id);

  const apiRequest: ListCampaignParams = {
    orderBy: JSON.stringify({
      totalViews: 'desc',
    }),
    skip: 0,
    take: 20,
    token: 'user',
    except: id as string,
  };

  const { data: dataCampaigns } = await store.dispatch(CampaignApi.endpoints.getListCampaign.initiate(apiRequest));
  const { data: bannerFooterPC } = await store.dispatch(
    bannerApi.endpoints.getBanner.initiate({ device: 'desktop', location: 'footer' })
  );
  const { data: bannerFooterMO } = await store.dispatch(
    bannerApi.endpoints.getBanner.initiate({ device: 'mobile', location: 'footer' })
  );

  const { data: dataCampaign } = await store.dispatch(
    CampaignApi.endpoints.getDetailCampaign.initiate({
      campaignId: id as string,
      token: 'user',
    })
  );
  if (!dataCampaign?.id) {
    // || (dataCampaign?.status !== 'PUBLIC' && dataCampaign?.status !== 'COMPLETION')
    return {
      notFound: true,
    };
  }
  return {
    props: {
      viewType: params?.slug?.[1] ?? 'detail',
      campaignDetail: dataCampaign ?? null,
      campaignsRecommend: dataCampaigns?.campaigns ?? null,
      dataBanner: {
        bannerFooterPC: bannerFooterPC?.data?.filter((i) => !!i?.images && i?.images?.length > 0) ?? null,
        bannerFooterMO: bannerFooterMO?.data?.filter((i) => !!i?.images && i?.images?.length > 0) ?? null,
      },
    },
  };
});

// eslint-disable-next-line max-lines-per-function
export default function CampaignDetailPage({
  campaignDetail,
  campaignsRecommend,
  viewType,
  dataBanner,
}: {
  campaignDetail: TypeCampaign | null;
  campaignsRecommend: TypeCampaign[] | null;
  viewType: 'completion' | 'winning' | 'losing' | 'detail';
  dataBanner: {
    bannerFooterPC: TypeBannerResponse['data'] | null;
    bannerFooterMO: TypeBannerResponse['data'] | null;
  };
}) {
  const { accessToken } = useSelector((state: RootState) => state.auth);
  const { width } = useWindowDimensions();
  useMeQuery(undefined, {
    skip: !accessToken,
  });

  return (
    <CampaignDetailProvider campaignDetail={campaignDetail} viewType={viewType}>
      <div className="font-notoSans">
        <CampaignDetail />
        {campaignDetail?.status === 'PUBLIC' && (
          <>
            <div className="h-[24px]" />
            {Array.isArray(campaignsRecommend) && campaignsRecommend?.length > 0 ? (
              <RecommedCampaignsSection campaignsRecommend={campaignsRecommend} dataBanner={dataBanner} />
            ) : (
              ''
            )}
            {width >= 1000 ? (
              <GoogleAd
                adClient="ca-pub-2602868131600259"
                // adFormat="auto"
                adSlot="**********"
                style={{ width: 970, height: 90, marginTop: 80 }}
              />
            ) : (
              <GoogleAd
                adClient="ca-pub-2602868131600259"
                adSlot="**********"
                style={{ width: 336, height: 250, marginTop: 80 }}
              />
            )}
          </>
        )}
      </div>
    </CampaignDetailProvider>
  );
}
