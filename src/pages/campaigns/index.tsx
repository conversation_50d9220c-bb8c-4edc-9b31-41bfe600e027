/* eslint-disable react/no-array-index-key */
/* eslint-disable no-nested-ternary */
/* eslint-disable @typescript-eslint/no-explicit-any */
import CampaignCardItem from '@/components/CampaignCardItem';
import CampaignCardItemSkeleton from '@/components/CampaignCardItem/CampaignCardItemSkeleton';

import PaginationRouterControl from '@/components/common/BasicPaination/PaginationRouterControl';
import CButtonShadow from '@/components/common/CButtonShadow';
import LossSvg from '@/components/common/icons/LossSvg';
import GoogleAd from '@/components/GoogleAd';
import TemplateBanner from '@/components/Home/TemplateBanner';
import useGetCampaigns from '@/hooks/useGetCampaigns';
import useWindowDimensions from '@/hooks/useWindowDimensions';
import { bannerApi, TypeBannerResponse } from '@/redux/endpoints/banner';
import { wrapper } from '@/redux/store';

import clsx from 'clsx';
import { useRouter } from 'next/router';
import { useMemo } from 'react';
import { isMobile } from 'react-device-detect';
import { useMediaQuery } from 'usehooks-ts';

export const getServerSideProps = wrapper.getServerSideProps((store) => async ({ query }) => {
  const { data: bannerFooterPC } = await store.dispatch(
    bannerApi.endpoints.getBanner.initiate({ device: 'desktop', location: 'footer' })
  );
  const { data: bannerFooterMO } = await store.dispatch(
    bannerApi.endpoints.getBanner.initiate({ device: 'mobile', location: 'footer' })
  );
  return {
    props: {
      orderByQuery: query.orderBy ?? '',
      dataBanner: {
        bannerFooterPC: bannerFooterPC?.data?.filter((i) => !!i?.images && i?.images?.length > 0) ?? null,
        bannerFooterMO: bannerFooterMO?.data?.filter((i) => !!i?.images && i?.images?.length > 0) ?? null,
      },
    },
  };
});

interface Props {
  orderByQuery: string;
  dataBanner: {
    bannerFooterPC: TypeBannerResponse['data'] | null;
    bannerFooterMO: TypeBannerResponse['data'] | null;
  };
}

export default function CampaignsPage({ orderByQuery, dataBanner }: Props) {
  const matchesMD = useMediaQuery('(min-width: 768px)');
  const PAGE_SIZE = isMobile ? 10 : 20;
  const { campaigns, total, isFetching } = useGetCampaigns({ pageSize: PAGE_SIZE });
  const { width } = useWindowDimensions();

  const router = useRouter();

  const orderBtnGroups = useMemo(
    () => [
      {
        label: '人気順',
        urlQuery: 'totalViews',
        isActive: orderByQuery === 'totalViews' || !orderByQuery,
      },
      {
        label: '新着順',
        urlQuery: 'startTime',
        isActive: orderByQuery === 'startTime',
      },
      {
        label: '報酬額順',
        urlQuery: 'totalPrizeValue',
        isActive: orderByQuery === 'totalPrizeValue',
      },
      {
        label: '終了間近',
        urlQuery: 'expiredTime',
        isActive: orderByQuery === 'expiredTime',
      },
    ],
    [orderByQuery]
  );

  return (
    <>
      <div className="font-notoSans text-main-text min-h-screen py-[32px] xl:py-[80px] bg-[#D5FFFF] ">
        <div className="grid grid-cols-2 md:grid-cols-4 gap-[8px] w-fit md:max-w-[680px] xl:max-w-[1025px] xxl:max-w-[1370px]  mx-auto">
          {orderBtnGroups.map((item, index) => (
            <div className="w-[126px] h-[51px] md:w-[164px] xl:w-[172px] md:h-[56px]" key={index}>
              <CButtonShadow
                classBgColor={item?.isActive ? 'bg-[#333]' : 'bg-white'}
                classShadowColor={item?.isActive ? 'bg-[#fff]' : 'bg-[#333]'}
                onClick={() => {
                  router.push(`/campaigns?page=1&orderBy=${item?.urlQuery}`);
                }}
                textClass={clsx(' text-[13px] font-bold ', item?.isActive ? 'text-white' : 'text-[#333]')}
                title={item?.label}
                withIcon={
                  item?.isActive
                    ? {
                        position: 'left',
                        icon: (
                          <svg fill="none" height="9" viewBox="0 0 12 9" width="12" xmlns="http://www.w3.org/2000/svg">
                            <path d="M1 4.5L3.5 7.5L11 1.5" stroke="#333333" strokeLinecap="round" strokeWidth="2" />
                            <path d="M1 4.5L3.5 7.5L11 1.5" stroke="white" strokeLinecap="round" strokeWidth="2" />
                          </svg>
                        ),
                      }
                    : undefined
                }
              />
            </div>
          ))}
        </div>
        <div className="h-[32px] xl:h-[80px]" />

        <div>
          {campaigns === null || isFetching ? (
            <div className="grid grid-cols-[repeat(auto-fit,_310px)] xs:grid-cols-[repeat(auto-fit,_335px)]  gap-[10px] justify-center md:max-w-[680px] xl:max-w-[1025px] xxl:max-w-[1370px] md:mx-auto">
              <CampaignCardItemSkeleton viewMode={matchesMD ? 'HAS_IMAGE' : 'NO_IMAGE'} />
              <CampaignCardItemSkeleton viewMode={matchesMD ? 'HAS_IMAGE' : 'NO_IMAGE'} />
              <CampaignCardItemSkeleton viewMode={matchesMD ? 'HAS_IMAGE' : 'NO_IMAGE'} />
              <div className="xl:hidden xxl:block">
                <CampaignCardItemSkeleton viewMode={matchesMD ? 'HAS_IMAGE' : 'NO_IMAGE'} />
              </div>
            </div>
          ) : Array.isArray(campaigns) && campaigns?.length > 0 ? (
            <>
              <div className="grid grid-cols-[repeat(auto-fit,_310px)] xs:grid-cols-[repeat(auto-fit,_335px)]  gap-[10px] justify-center md:max-w-[680px] xl:max-w-[1025px] xxl:max-w-[1370px] md:mx-auto">
                {campaigns?.map((item) => (
                  <CampaignCardItem item={item as any} key={item.id} viewMode={matchesMD ? 'HAS_IMAGE' : 'NO_IMAGE'} />
                ))}
              </div>
              <div className="h-[32px] xxl:h-[80px]" />
              <PaginationRouterControl pageSize={PAGE_SIZE} total={total ?? 0} />
            </>
          ) : (
            <div className="pt-[100px] flex flex-col items-center justify-center gap-4">
              <LossSvg />
              <p className="text-[20px] font-bold text-center">データがありません</p>

              <div className="h-[53px] w-[165px] mx-auto mt-[16px]">
                <CButtonShadow
                  onClick={() => {
                    router.push('/');
                  }}
                  title="HOMEに戻る"
                  type="button"
                />
              </div>
            </div>
          )}
          {width >= 1000 ? (
            <GoogleAd
              adClient="ca-pub-2602868131600259"
              // adFormat="auto"
              adSlot="4756571102"
              style={{ width: 970, height: 90, marginTop: 80 }}
            />
          ) : (
            <GoogleAd
              adClient="ca-pub-2602868131600259"
              adSlot="9200088853"
              style={{ width: 336, height: 250, marginTop: 80 }}
            />
          )}
        </div>
      </div>
      {width > 1024 && dataBanner?.bannerFooterPC && dataBanner?.bannerFooterPC?.length > 0 ? (
        <div className="pt-[72px] mb-[-28px]">
          <TemplateBanner banner={dataBanner?.bannerFooterPC} device="desktop" location="footer" />
        </div>
      ) : (
        ''
      )}
      {width <= 1024 && dataBanner?.bannerFooterMO && dataBanner?.bannerFooterMO?.length > 0 ? (
        <div className="pt-[56px] md:pt-[72px] md:mb-[-28px]">
          <TemplateBanner banner={dataBanner?.bannerFooterMO} device="mobile" location="footer" />
        </div>
      ) : (
        ''
      )}
    </>
  );
}
