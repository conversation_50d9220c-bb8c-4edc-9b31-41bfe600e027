.loader {
  :global {
    position: relative;
    padding: 15px 30px;
    width: fit-content;
    overflow: hidden;
    span:nth-child(1) {
      position: absolute;
      width: 100%;
      height: 3px;
      top: 0;
      left: 0;
      background: #04afaf; //linear-gradient(to right, #20ffff, #71ff6f);
      animation: animate1 2s linear infinite;
    }
    span:nth-child(2) {
      position: absolute;
      width: 3px;
      height: 100%;
      top: 0;
      right: 0;
      background: #04afaf; //linear-gradient(to bottom, #20ffff, #71ff6f);
      animation: animate2 2s linear infinite;
      animation-delay: 1s;
    }
    span:nth-child(3) {
      position: absolute;
      width: 100%;
      height: 3px;
      left: 0;
      bottom: 0;
      background: #04afaf; //linear-gradient(to left, #20ffff, #71ff6f);
      animation: animate3 2s linear infinite;
    }
    span:nth-child(4) {
      position: absolute;
      width: 3px;
      height: 100%;
      top: 0;
      left: 0;
      background: #04afaf; // linear-gradient(to top, #20ffff, #71ff6f);
      animation: animate4 2s linear infinite;
      animation-delay: 1s;
    }
  }
}
