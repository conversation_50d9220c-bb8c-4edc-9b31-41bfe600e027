/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/naming-convention */
/* eslint-disable no-console */

'use client';

import { useConnectTwitterMutation, useLazyMeQuery } from '@/redux/endpoints/auth';
import clsx from 'clsx';
import Image from 'next/image';

import { useCallback, useEffect } from 'react';
import { getErrorMessage } from '@/utils/func/getErrorMessage';
import { useDispatch } from 'react-redux';
import { setSession } from '@/redux/slices/auth.slice';
import toastMessage from '@/utils/func/toastMessage';
import { useRouter } from 'next/router';
import { REDIRECT_QUERY_KEY } from '@/utils/constant/enums';
import styles from './styles.module.scss';

export default function TwitterAuthCallBack() {
  const searchParams = new URLSearchParams(window.location.search);
  const stateQuery = searchParams.get('state');
  const code = searchParams.get('code');

  const dispatch = useDispatch();
  const router = useRouter();

  const [triggerGetMe] = useLazyMeQuery();

  const [connectTwitter] = useConnectTwitterMutation();
  // const handleTwitterAuth = useCallback(async () => {
  //   try {
  //     const stateUrl = stateQuery?.split('+++');
  //     const redirectUri = stateUrl?.[0];
  //     const state = stateUrl?.[1];

  //     if (redirectUri && (state === 'SIGNUP' || state === 'SIGNIN' || state === 'CONNECT') && code) {
  //       const request = {
  //         body: {
  //           code,
  //           state: state === 'SIGNUP' || state === 'SIGNIN' ? 'SIGNIN' : 'CONNECT',
  //           redirect_uri: redirectUri,
  //         },
  //       };
  //       if (state !== 'CONNECT') {
  //         (request as any).params = {
  //           token: 'user',
  //         };
  //       }
  //       const resp = await connectTwitter(request).unwrap();

  //       if (
  //         (resp?.accessToken && resp?.refreshToken && resp?.user) ||
  //         (resp?.user && resp?.totpToken) ||
  //         resp?.status === true
  //       ) {
  //         localStorage.setItem(
  //           'twitter_callback_data',
  //           JSON.stringify({
  //             data: resp,
  //           })
  //         );
  //       } else {
  //         localStorage.setItem(
  //           'twitter_callback_data',
  //           JSON.stringify({
  //             error: 'Twitterに接続できません。',
  //           })
  //         );
  //       }
  //     } else {
  //       localStorage.setItem(
  //         'twitter_callback_data',
  //         JSON.stringify({
  //           error: 'Twitter に接続するためのパラメータが不足しています。',
  //         })
  //       );
  //     }
  //   } catch (err: any) {
  //     localStorage.setItem(
  //       'twitter_callback_data',
  //       JSON.stringify({
  //         error: getErrorMessage(err),
  //       })
  //     );
  //   } finally {
  //     window.close();
  //   }
  // }, []);

  const onRedirectUrl = (url: string) => {
    setTimeout(() => {
      if (url) {
        window.location.assign(url);
      } else {
        router.replace('/');
      }
    }, 2500);
  };

  const handleTwitterAuthV1 = useCallback(async () => {
    const stateUrl = stateQuery?.split('+++');
    const redirectUri = stateUrl?.[0];
    const state = stateUrl?.[1];

    const authFromUrl = stateUrl?.[2];
    const callBackPath = stateUrl?.[3];
    const userType = stateUrl?.[4];
    try {
      if (redirectUri && (state === 'SIGNUP' || state === 'SIGNIN' || state === 'CONNECT') && code) {
        const request = {
          body: {
            code,
            state: state === 'SIGNUP' || state === 'SIGNIN' ? 'SIGNIN' : 'CONNECT',
            redirect_uri: redirectUri,
          },
        };
        if (state !== 'CONNECT') {
          (request as any).params = {
            token: 'user',
          };
        }
        const resp = await connectTwitter(request).unwrap();

        if (
          (resp?.accessToken && resp?.refreshToken && resp?.user) ||
          (resp?.user && resp?.totpToken) ||
          resp?.status === true
        ) {
          switch (state) {
            case 'SIGNIN':
            case 'SIGNUP':
              if (resp?.accessToken && resp?.refreshToken && resp?.user) {
                dispatch(setSession({ ...resp }));
                if (callBackPath) {
                  toastMessage('X(Twitter)アカウントが連携されました。', 'success');
                  window.location.assign(`${window.location.origin}${callBackPath}`);
                } else {
                  router.replace('/my-page');
                }
                return;
              }
              if (resp?.totpToken && resp?.user) {
                router.replace(
                  `/auth/sign-in/${
                    userType === 'creator' ? 'campaign-creator' : 'campaign-implementer'
                  }/verification?totpToken=${resp?.totpToken ?? ''}&userId=${
                    resp?.user?.id ?? ''
                  }&authMethod=twitter&${REDIRECT_QUERY_KEY}=${callBackPath ?? ''}`
                );
                return;
              }
              router.replace('/');
              break;
            case 'CONNECT':
              await triggerGetMe().unwrap();
              toastMessage('X(Twitter)アカウントが連携されました。', 'success');
              if (callBackPath) {
                window.location.assign(`${window.location.origin}${callBackPath}`);
                return;
              }
              onRedirectUrl(authFromUrl ?? '');
              break;
            default:
              onRedirectUrl(authFromUrl ?? '');
              break;
          }
        } else {
          toastMessage('Twitterに接続できません。', 'error');
          onRedirectUrl(authFromUrl ?? '');
        }
      } else {
        toastMessage('Twitter に接続するためのパラメータが不足しています。', 'error');
        onRedirectUrl(authFromUrl ?? '');
      }
    } catch (err: any) {
      toastMessage(getErrorMessage(err), 'error');
      onRedirectUrl(authFromUrl ?? '');
    }
  }, []);
  useEffect(() => {
    handleTwitterAuthV1();
  }, [handleTwitterAuthV1]);
  return (
    <div className="min-h-[calc(100vh-64px)] w-full h-full text-center flex justify-center items-center">
      <div className={clsx(styles.loader)}>
        <span />
        <span />
        <span />
        <span />
        <div className="w-[101px] h-[30px]">
          <Image
            alt="footer logo"
            className="w-full h-full object-cover"
            height={30}
            src="/assets/images/logo 1.png"
            width={101}
          />
        </div>
      </div>
    </div>
  );
}
