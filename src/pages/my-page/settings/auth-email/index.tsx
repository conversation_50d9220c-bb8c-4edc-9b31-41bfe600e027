import AuthCheck from '@/components/AuthCheck';
import EmailVerificationModal from '@/components/auth/email-verification-modal';
import CButtonShadow from '@/components/common/CButtonShadow';
import CFormInputShadow from '@/components/common/CFormInputShadow';
import useAuthEmailPassword from '@/hooks/useAuthEmailPassword';
import { useEmailSendOtpMutation } from '@/redux/endpoints/auth';
import { useUpdateMeMutation } from '@/redux/endpoints/me';
import { RootState } from '@/redux/store';
import { REDIRECT_QUERY_KEY } from '@/utils/constant/enums';
import { getErrorMessage } from '@/utils/func/getErrorMessage';
import toastMessage from '@/utils/func/toastMessage';
import { AuthEmailPasswordData } from '@/utils/schema/auth.schema';
import { GOOGLE_RECAPTCHA } from '@/utils/site-configs';
import { Spin } from 'antd';
import clsx from 'clsx';
import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';
import ReCAPTCHA from 'react-google-recaptcha';
import { useSelector } from 'react-redux';

export default function SettingAuthEmailPage() {
  const router = useRouter();

  const [updateMe, { isLoading, isError: isUpdateMeError }] = useUpdateMeMutation();

  const { register, handleSubmit, errors, isDisableSubmit, onChangeRecaptcha } = useAuthEmailPassword();

  const { accessToken, user } = useSelector((state: RootState) => state.auth);
  const [isOpenVerifiModal, setIsOpenVerifiModal] = useState(false);
  const [newData, setNewData] = useState({
    email: '',
    password: '',
  });
  const [onSendOtp, { isLoading: isSendingOtp }] = useEmailSendOtpMutation();

  const onUpdateEmail = async (data: AuthEmailPasswordData) => {
    try {
      if (data?.email && data?.password) {
        await onSendOtp({
          email: data?.email,
          userId: user?.id ?? 0,
        }).unwrap();

        setNewData({
          email: data.email,
          password: data.password,
        });

        setIsOpenVerifiModal(true);
      }
    } catch (err) {
      toastMessage(getErrorMessage(err), 'error');
    }
  };

  const onCancelVerifiEmail = () => {
    setNewData({
      email: '',
      password: '',
    });
    setIsOpenVerifiModal(false);
  };

  const handleResendCode = async () => {
    try {
      if (newData?.email) {
        await onSendOtp({
          email: newData?.email,
          userId: user?.id ?? 0,
        }).unwrap();
      }
    } catch (e) {
      toastMessage(getErrorMessage(e), 'error');
    }
  };

  const handleSubmitCode = async (code: string) => {
    try {
      await updateMe({
        email: newData?.email?.toLowerCase(),
        newPassword: newData?.password,
        code,
      }).unwrap();
      toastMessage('メールアドレス、パスワードが正常に更新されました。', 'success');

      const redirectUrl = router?.query?.[`${REDIRECT_QUERY_KEY}`];
      if (redirectUrl && typeof redirectUrl === 'string' && redirectUrl.includes('/')) {
        window.location.assign(`${window.location.origin}${redirectUrl}`);
      } else {
        router.push('/my-page');
      }
    } catch (e) {
      toastMessage(getErrorMessage(e), 'error');
    } finally {
      setIsOpenVerifiModal(false);
      setNewData({
        email: '',
        password: '',
      });
    }
  };

  useEffect(() => {
    if (accessToken && (user?.email?.email || user?.havePassword)) {
      router.replace('/my-page');
    }
  }, [user?.email?.email, user?.havePassword, accessToken]);

  return (
    <>
      <AuthCheck>
        <div
          className={clsx(
            'container-min-height pb-[56px] h-full w-full bg-[#D5FFFF] py-[40px] px-[20px] transition-all duration-300'
          )}
        >
          <Spin spinning={isLoading}>
            <form className="max-w-[335px] mx-auto" onSubmit={handleSubmit(onUpdateEmail)}>
              <h1 className="text-[20px] font-bold text-[#04AFAF] tracking-[0.6px] text-center ">
                メール・パスワード登録
              </h1>
              <p className="text-gray-1 text-[13px] leading-[22px] tracking-[0.39px]">
                ※キャンペーンの応募・キャンペーン作成にはメールアドレス/パスワードの登録が必要です。
              </p>
              <div className="h-[32px]" />
              <div className="bg-white border-[2px] border-[#333] px-[24px] py-[40px] rounded-[16px]">
                <div className="w-full flex flex-col items-center">
                  <CFormInputShadow
                    errors={errors}
                    name="email"
                    placeholder="メールアドレスを入力"
                    register={register}
                  />
                  <div className="h-[8px]" />
                  <CFormInputShadow
                    errors={errors}
                    name="password"
                    placeholder="パスワードを入力"
                    register={register}
                    type="password"
                  />
                  <div className="h-[16px]" />
                  <ReCAPTCHA onChange={onChangeRecaptcha} sitekey={GOOGLE_RECAPTCHA.KEY} />
                </div>
                <div className="h-[24px]" />
                <div className="h-[53px] flex gap-[8px]">
                  <div className="flex-1">
                    <CButtonShadow
                      classBgColor={isDisableSubmit ? 'bg-[#c2c2c2]' : 'bg-[#333]'}
                      classBorderColor={isDisableSubmit ? 'border-[#c2c2c2]' : 'border-[#333]'}
                      classShadowColor="bg-[#fff]"
                      isDisable={isDisableSubmit}
                      textClass="text-white text-[14px] font-notoSans"
                      title="登録する"
                      type="submit"
                    />
                  </div>
                </div>
              </div>
            </form>
          </Spin>
        </div>
      </AuthCheck>
      <EmailVerificationModal
        emailVerification={newData.email}
        isOpen={isOpenVerifiModal}
        isSubmitError={isUpdateMeError}
        isSubmitting={isLoading || isSendingOtp}
        onCancel={onCancelVerifiEmail}
        onResendCode={handleResendCode}
        onSubmitCode={handleSubmitCode}
      />
    </>
  );
}
