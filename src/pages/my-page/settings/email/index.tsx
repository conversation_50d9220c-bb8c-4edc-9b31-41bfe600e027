import AuthCheck from '@/components/AuthCheck';
import EmailVerificationModal from '@/components/auth/email-verification-modal';
import CButtonShadow from '@/components/common/CButtonShadow';
import CFormInputShadow from '@/components/common/CFormInputShadow';
import { useEmailSendOtpMutation } from '@/redux/endpoints/auth';
import { useUpdateMeMutation } from '@/redux/endpoints/me';
import { RootState } from '@/redux/store';
import { REDIRECT_QUERY_KEY } from '@/utils/constant/enums';
import { getErrorMessage } from '@/utils/func/getErrorMessage';
import toastMessage from '@/utils/func/toastMessage';
import { emailSchema, UpdateEmailData } from '@/utils/schema/auth.schema';
import { yupResolver } from '@hookform/resolvers/yup';
import { Spin } from 'antd';
import clsx from 'clsx';
import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { useSelector } from 'react-redux';

export default function SettingEmailPage() {
  const router = useRouter();

  const [updateMe, { isLoading, isError: isUpdateMeError }] = useUpdateMeMutation();

  const {
    register,
    handleSubmit,

    formState: { errors },
  } = useForm<UpdateEmailData>({
    resolver: yupResolver(emailSchema),
    mode: 'onChange',
  });

  const { accessToken, user } = useSelector((state: RootState) => state.auth);
  const [isOpenVerifiModal, setIsOpenVerifiModal] = useState(false);
  const [newEmail, setNewEmail] = useState('');
  const [onSendOtp, { isLoading: isSendingOtp }] = useEmailSendOtpMutation();

  const onUpdateEmail = async (data: UpdateEmailData) => {
    try {
      if (data?.email) {
        await onSendOtp({
          email: data?.email,
          userId: user?.id ?? 0,
        }).unwrap();

        setNewEmail(data?.email);

        setIsOpenVerifiModal(true);
      }
    } catch (err) {
      setNewEmail('');
      toastMessage(getErrorMessage(err), 'error');
    }
  };

  const onCancelVerifiEmail = () => {
    setNewEmail('');
    setIsOpenVerifiModal(false);
  };

  const handleResendCode = async () => {
    try {
      if (newEmail) {
        await onSendOtp({
          email: newEmail,
          userId: user?.id ?? 0,
        }).unwrap();
      }
    } catch (e) {
      toastMessage(getErrorMessage(e), 'error');
    }
  };

  const handleSubmitCode = async (code: string) => {
    try {
      await updateMe({
        email: newEmail.toLowerCase(),
        code,
      }).unwrap();
      toastMessage('メールアドレスの更新に成功されました。', 'success');
      const redirectUrl = router?.query?.[`${REDIRECT_QUERY_KEY}`];
      if (redirectUrl && typeof redirectUrl === 'string' && redirectUrl.includes('/')) {
        window.location.assign(`${window.location.origin}${redirectUrl}`);
      } else {
        router.push('/my-page');
      }
    } catch (e) {
      toastMessage(getErrorMessage(e), 'error');
    } finally {
      setIsOpenVerifiModal(false);
      setNewEmail('');
    }
  };

  useEffect(() => {
    if (accessToken && !user?.email?.email && !user?.havePassword) {
      router.replace('/my-page/settings/auth-email');
    }
  }, [user?.email?.email, user?.havePassword, accessToken]);

  return (
    <>
      <AuthCheck>
        <div
          className={clsx(
            'container-min-height pb-[56px] w-full bg-[#D5FFFF] py-[40px] px-[20px] transition-all duration-300'
          )}
        >
          <Spin spinning={isLoading}>
            <form onSubmit={handleSubmit(onUpdateEmail)}>
              <div className="max-w-[335px] mx-auto">
                <div className="bg-white border-[2px] border-[#333] px-[22px] py-[30px] rounded-[16px]">
                  <h1 className="text-[20px] font-bold text-[#04AFAF] tracking-[0.6px] text-center ">
                    新しいメールアドレスを入力
                  </h1>
                  <div className="h-[16px]" />
                  <CFormInputShadow
                    errors={errors}
                    name="email"
                    placeholder="メールアドレスを入力"
                    register={register}
                    type="text"
                  />
                </div>
                <div className="h-[24px]" />
                <div className="h-[53px] flex gap-[8px]">
                  <div className="flex-1">
                    <CButtonShadow
                      classBgColor="bg-white"
                      classShadowColor="bg-[#333]"
                      onClick={() => {
                        router.back();
                      }}
                      textClass="text-[#333] text-[16px] font-notoSans"
                      title="キャンセルする"
                      type="button"
                    />
                  </div>
                  <div className="flex-1">
                    <CButtonShadow formNoValidate title="保存する" type="submit" />
                  </div>
                </div>
              </div>
            </form>
          </Spin>
        </div>
      </AuthCheck>
      <EmailVerificationModal
        emailVerification={newEmail}
        isOpen={isOpenVerifiModal}
        isSubmitError={isUpdateMeError}
        isSubmitting={isLoading || isSendingOtp}
        onCancel={onCancelVerifiEmail}
        onResendCode={handleResendCode}
        onSubmitCode={handleSubmitCode}
      />
    </>
  );
}
