import HomePage from '@/components/Home';
import { CampaignApi, ListCampaignParams, TypeCampaign } from '@/redux/endpoints/campaign';
import { bannerApi, TypeBannerResponse } from '@/redux/endpoints/banner';
import { wrapper } from '@/redux/store';
import { HOME_PAGINATION_PAGE_SIZE } from '@/utils/constant/enums';

export const getServerSideProps = wrapper.getServerSideProps((store) => async () => {
  const apiRequest: ListCampaignParams = {
    skip: 0,
    take: HOME_PAGINATION_PAGE_SIZE,
    token: 'user',
  };

  const { data: campaignsOrderByViews } = await store.dispatch(
    CampaignApi.endpoints.getListCampaign.initiate({
      ...apiRequest,
      orderBy: JSON.stringify({
        totalViews: 'desc',
      }),
    })
  );

  const { data: campaignsOrderByStartTime } = await store.dispatch(
    CampaignApi.endpoints.getListCampaign.initiate({
      ...apiRequest,
      orderBy: JSON.stringify({
        startTime: 'desc',
      }),
    })
  );

  const { data: bannerTopPC } = await store.dispatch(
    bannerApi.endpoints.getBanner.initiate({ device: 'desktop', location: 'top' })
  );
  const { data: bannerTopMO } = await store.dispatch(
    bannerApi.endpoints.getBanner.initiate({ device: 'mobile', location: 'top' })
  );
  const { data: bannerFooterPC } = await store.dispatch(
    bannerApi.endpoints.getBanner.initiate({ device: 'desktop', location: 'footer' })
  );
  const { data: bannerFooterMO } = await store.dispatch(
    bannerApi.endpoints.getBanner.initiate({ device: 'mobile', location: 'footer' })
  );

  const { data: campaignsOrderByTotalPrizeValue } = await store.dispatch(
    CampaignApi.endpoints.getListCampaign.initiate({
      ...apiRequest,
      orderBy: JSON.stringify({
        totalPrizeValue: 'desc',
      }),
    })
  );

  return {
    props: {
      campaignsOrderByViews: campaignsOrderByViews?.campaigns ?? null,
      campaignsOrderByStartTime: campaignsOrderByStartTime?.campaigns ?? null,
      campaignsOrderByTotalPrizeValue: campaignsOrderByTotalPrizeValue?.campaigns ?? null,
      dataBanner: {
        bannerTopPC: bannerTopPC?.data?.filter((i) => !!i?.images && i?.images?.length > 0) ?? null,
        bannerTopMO: bannerTopMO?.data?.filter((i) => !!i?.images && i?.images?.length > 0) ?? null,
        bannerFooterPC: bannerFooterPC?.data?.filter((i) => !!i?.images && i?.images?.length > 0) ?? null,
        bannerFooterMO: bannerFooterMO?.data?.filter((i) => !!i?.images && i?.images?.length > 0) ?? null,
      },
    },
  };
});
interface IPageProps {
  campaignsOrderByViews: TypeCampaign[] | null;
  // campaignsOrderByStartTime: TypeCampaign[] | null;
  campaignsOrderByTotalPrizeValue: TypeCampaign[] | null;
  dataBanner: {
    bannerTopPC: TypeBannerResponse['data'] | null;
    bannerTopMO: TypeBannerResponse['data'] | null;
    bannerFooterPC: TypeBannerResponse['data'] | null;
    bannerFooterMO: TypeBannerResponse['data'] | null;
  };
}

export default function Home({
  campaignsOrderByViews,
  // campaignsOrderByStartTime,
  campaignsOrderByTotalPrizeValue,
  dataBanner,
}: IPageProps) {
  return (
    <HomePage
      // campaignsOrderByStartTime={campaignsOrderByStartTime}
      campaignsOrderByTotalPrizeValue={campaignsOrderByTotalPrizeValue}
      campaignsOrderByViews={campaignsOrderByViews}
      dataBanner={dataBanner}
    />
  );
}
