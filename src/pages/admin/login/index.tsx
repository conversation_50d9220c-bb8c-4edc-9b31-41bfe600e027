import BasicButton from '@/components/common/BasicButton';
import BasicInput from '@/components/common/BasicInput';
import { useSigninEmailMutation } from '@/redux/endpoints/auth';
import { setSession } from '@/redux/slices/auth.slice';
import { AuthEmailPasswordData } from '@/utils/schema/auth.schema';
import { Card, Form, Layout } from 'antd';
import { useRouter } from 'next/router';
import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '@/redux/store';

const App: React.FC = () => {
  const [form] = Form.useForm();
  const { user } = useSelector((state: RootState) => state.auth);
  const [errorLogin, setErrorLogin] = useState<string>();
  const [signinEmail] = useSigninEmailMutation();
  const [isSubmit, setIsSubmit] = useState<boolean>(false);
  const router = useRouter();
  const dispatch = useDispatch();

  useEffect(() => {
    if (user && user?.isAdmin) {
      router.push('/admin');
    }
  }, [user]);

  const handleLoginAdmin = async (value: AuthEmailPasswordData) => {
    if (value) {
      setIsSubmit(true);
      try {
        const data = await signinEmail(value).unwrap();
        if (data && data?.user?.isAdmin) {
          dispatch(setSession({ ...data }));
          setTimeout(() => {
            router.replace('/admin');
          }, 300);
        } else {
          setErrorLogin('メールアドレスまたはパスワードを間違えています！');
        }
        setIsSubmit(false);
      } catch (error) {
        setIsSubmit(false);
        setErrorLogin('メールアドレスまたはパスワードを間違えています！');
      }
    }
  };

  return (
    <Layout className="min-h-[100vh] flex justify-center items-center">
      <Card className="w-full max-w-[600px]">
        <h1 className="font-bold text-[24px] mb-5">サイト管理者ログイン</h1>
        <Form
          form={form}
          layout="vertical"
          onFinish={(value) => {
            handleLoginAdmin(value);
          }}
        >
          <Form.Item
            label="メールアドレス"
            name="email"
            rules={[
              { required: true, message: 'メールアドレスを入力!' },
              { type: 'email', message: '間違った電子メール形式' },
            ]}
          >
            <BasicInput placeholder="メールアドレスを入力" type="email" />
          </Form.Item>
          <Form.Item
            label="パスワード"
            name="password"
            rules={[
              { required: true, message: 'パスワードを入力してください' },
              { min: 8, message: '8文字以上' },
            ]}
          >
            <BasicInput placeholder="パスワードを入力" type="password" />
          </Form.Item>
          <Form.Item className="mb-[8px]">
            <BasicButton className="w-fit h-[48px] mt-[24px]" disabled={isSubmit} htmlType="submit" loading={isSubmit}>
              ログイン
            </BasicButton>
          </Form.Item>
          <div className="text-[#ff4d4f]">{errorLogin}</div>
        </Form>
      </Card>
    </Layout>
  );
};

export default App;
