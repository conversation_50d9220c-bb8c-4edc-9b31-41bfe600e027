/* eslint-disable react/jsx-no-useless-fragment */
import Maintenance from '@/components/layout/_core/Maintenance';
import { DataMaintenanceItem, useGetMaintenanceQuery } from '@/redux/endpoints/maintenance';
import dayjs from 'dayjs';
import Head from 'next/head';
import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';

export default function MaintenancePage() {
  const router = useRouter();
  const { data: campaignDetailTasks } = useGetMaintenanceQuery({});
  const [maintenance, setMaintenance] = useState<DataMaintenanceItem | undefined>();

  useEffect(() => {
    if (campaignDetailTasks?.data?.data) {
      const activeMaintenance = campaignDetailTasks?.data?.data?.find((item) => item);
      if (
        activeMaintenance &&
        dayjs(activeMaintenance.endAt).unix() > dayjs().unix() &&
        dayjs(activeMaintenance.startAt).unix() <= dayjs().unix()
      ) {
        setMaintenance(activeMaintenance);
      } else {
        router.push('/');
      }
    }
  }, [campaignDetailTasks]);

  return (
    <>
      <Head>
        <meta content="noindex, nofollow, noarchive" name="robots" />
        <meta content="noindex" name="googlebot" />
      </Head>

      {maintenance && <Maintenance data={maintenance} />}
    </>
  );
}
