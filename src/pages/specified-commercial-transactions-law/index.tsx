import Link from 'next/link';
import React from 'react';

function SpecifiedCommercialTransactionsLawPage() {
  return (
    <div className="px-[20px] max-w-[820px] mx-auto">
      <div className="flex flex-col items-center space-y-[32px] mt-[40px]">
        <h2 className="text-[28px] font-bold text-[#04AFAF] text-center">
          特定商取引法に
          <br /> 基づく表示
        </h2>
      </div>
      <div className="flex flex-col space-y-[32px] text-[13px] text-[#777] mt-[32px]">
        <div className="flex flex-col space-y-[8px]">
          <span className="font-bold">事業者</span>
          <span>株式会社clout</span>
        </div>
        {/* <div className="flex flex-col space-y-[8px]">
          <span className="font-bold">代表者</span>
          <span>岩島 慶</span>
        </div> */}
        <div className="flex flex-col space-y-[8px]">
          <span className="font-bold">所在地</span>
          <span>〒107-0062 東京都港区南青山3丁目1番36号青山丸竹ビル6F</span>
        </div>
        <div className="flex flex-col space-y-[8px]">
          <span className="font-bold">メールアドレス</span>
          <span className="text-[#04AFAF] font-medium">
            <a href="mailto:<EMAIL>"><EMAIL></a>
          </span>
        </div>
        <div className="flex flex-col space-y-[8px]">
          <span className="font-bold">お問い合わせ</span>
          <span>
            サービスについてのお問い合わせは
            <Link className="text-[#04AFAF] font-medium" href="/inquiry">
              こちら
            </Link>
            からお願いします。
          </span>
        </div>
        <div className="flex flex-col space-y-[8px]">
          <span className="font-bold">サービス利用料</span>
          <span>キャンペーン作成時に入金をする金額に対して10%</span>
        </div>
        <div className="flex flex-col space-y-[8px]">
          <span className="font-bold">お支払い方法</span>
          <span>
            クレジットカード決済 Visa、Mastercard、American Express、JCB、Diners
            Club、Discoverと、主要な6カードブランドのクレジットカード、デビットカードに対応
          </span>
        </div>
        <div className="flex flex-col space-y-[8px]">
          <span className="font-bold">お支払い時期</span>
          <span>
            ご利用のクレジットカードの締め日や契約内容により異なります。ご利用されるカード会社にお問い合わせください。
          </span>
        </div>
        <div className="flex flex-col space-y-[8px]">
          <span className="font-bold">キャンペーン開始時期</span>
          <span>
            クレジットカード決済完了後、1〜2営業日でキャンペーンが開始されます。キャンペーンが開始されない場合、連絡をいただきましたら、遅延なく対応致します。
          </span>
        </div>
        <div className="flex flex-col space-y-[8px]">
          <span className="font-bold">動作保証環境</span>
          <span>iOS：iOS15.x、iOS16.x、iOS17.0.3~ </span>
          <span>端末：iPhoneXS、iPhone11、iPhone12、iPhone13、iPhone14 Pro</span>
          <span>ブラウザ：Safali</span>
        </div>
        <div className="flex flex-col space-y-[8px]">
          <span>Android：Android11、Android12、Android12L、Android13、Android14</span>
          <span>Google Chrome</span>
          <span>※Androidは端末が多岐に渡るため、端末は保証しておりません。</span>
        </div>
        <div className="flex flex-col space-y-[8px]">
          <span className="font-bold">返品・返金・キャンセルに関する特約</span>
          <span>
            本サイトで販売するサービスについては、クレジットカード決済完了後のお客様のご都合による返品又はキャンセルはできません。なお、当社は、キャンペーンに関連する応募者や他の第三者に発生した損害について、一切責任を負いません。問題がある場合は、直接キャンペーン主催者にお問い合わせください。詳細は
            <Link className="text-[#04AFAF] font-medium" href="/terms-of-service?view=implementer">
              こちら
            </Link>
          </span>
        </div>
      </div>
    </div>
  );
}

export default SpecifiedCommercialTransactionsLawPage;
