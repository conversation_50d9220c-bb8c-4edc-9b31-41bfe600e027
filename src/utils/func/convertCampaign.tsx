/* eslint-disable react/no-danger */
/* eslint-disable max-lines */
/* eslint-disable no-duplicate-case */
/* eslint-disable max-lines-per-function */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
import { TypeTask } from '@/redux/endpoints/campaign';
import { MasterDataResponse } from '@/redux/endpoints/masterData';
import { isMobile } from 'react-device-detect';
import toastMessage from './toastMessage';
import { getErrorMessage } from './getErrorMessage';
import {
  INSTAGRAM_BASE_URL,
  TIKTOK_BASE_URL,
  TWITTER_BASE_URL,
  TWITTER_COMPOSE_POST_BASE_URL,
  TWITTER_FOLLOW_BASE_URL,
  TWITTER_TWEET_POST_BASE_URL,
} from '../constant/enums';

export interface TasksConvert {
  id: number;
  campaignId: string;
  title: string;
  description: React.ReactNode;
  type: 'OPEN_LINK' | 'FAQ_FREE_TEXT' | 'FAQ_CHOOSE_ONE' | 'FAQ_CHOOSE_MULTIPLE' | 'SHARE_URL';
  link?: string;
  choose_options?: any;
  taskInfo?: any;
  required?: boolean;
  points?: number;
}

export function parseMilliseconds(ms: number) {
  let remain = ms;

  const days = Math.floor(remain / (1000 * 60 * 60 * 24));
  remain %= 1000 * 60 * 60 * 24;

  const hours = Math.floor(remain / (1000 * 60 * 60));
  remain %= 1000 * 60 * 60;

  const minutes = Math.floor(remain / (1000 * 60));
  remain %= 1000 * 60;

  const seconds = Math.floor(remain / 1000);
  remain %= 1000;

  const milliseconds = remain;

  return {
    days,
    hours,
    minutes,
    seconds,
    milliseconds,
  };
}

export const convertTextWithURL = (input: string) => {
  let result = '';
  if (input) {
    const arr = input?.split('\n');
    for (let i = 0; i < arr.length; i += 1) {
      if (
        arr[i]?.trim().startsWith('https://') ||
        arr[i]?.trim().startsWith('http://') ||
        arr[i]?.trim().startsWith('www.')
      ) {
        const link = arr[i]?.trim();
        arr[i] = `<a href='${link}' rel='noopener noreferrer' target='_blank' style="color: #04afaf;">${arr[i]}</a>`;
      }
    }
    result = arr.join('<br/>');
  }
  return result;
};

// Add isLink=true to the end of the link if it doesn't have it
const addIsLinkParam = (link: string) => {
  if (!link.includes('?') && (link.startsWith('http') || link.startsWith('https') || link.startsWith('www.'))) {
    return `${link}?isLink=true`;
  }
  return link;
};

export const getMasterDataLabel = (
  masterData: MasterDataResponse | null | undefined,
  masterDataKey: string,
  itemValue: string
) => {
  const result = '';
  try {
    if (masterData && masterDataKey && Object.prototype?.hasOwnProperty.call(masterData, masterDataKey)) {
      const item = masterData?.[masterDataKey as keyof MasterDataResponse]?.find(
        // eslint-disable-next-line eqeqeq
        (i) => i?.value === itemValue
      );
      return item?.label ?? '';
    }
  } catch (err: any) {
    toastMessage(getErrorMessage(err), 'error');
  }
  return result;
};

export const convertCampaignTask = (task: TypeTask | null) => {
  let result: TasksConvert | null = null;
  try {
    const taskPlatForm = task?.taskTemplate?.config?.platForm;
    switch (task?.type) {
      case 'TWITTER':
        switch (task?.taskTemplate?.config?.type) {
          case 'twitter_follow': {
            const targetUser = taskPlatForm === 'TWITTER' ? task?.taskTemplate?.config?.userFollow : '';
            let user = '';
            if (targetUser?.includes('twitter.com') || targetUser?.includes('x.com')) {
              const splitArr = targetUser?.split('/');
              user = splitArr?.[splitArr.length - 1] ?? '';
            } else {
              user = targetUser?.charAt(0) === '@' ? targetUser?.slice(1) : targetUser;
            }
            const linkByOs = isMobile
              ? `${TWITTER_BASE_URL}/${user}`
              : `${TWITTER_FOLLOW_BASE_URL}?screen_name=${user}`;

            result = {
              id: task?.id ?? '',
              campaignId: task?.campaignId ?? '',
              title: 'X(Twitter)でフォローする',
              required: task?.taskTemplate?.required,
              points: task?.taskTemplate?.points,
              description: (
                <div className="text-[14px]">
                  <p>
                    <span className="font-bold">{targetUser ?? ''}</span>のアカウントをフォローしてください。
                  </p>
                </div>
              ),
              type: 'OPEN_LINK',
              link: linkByOs,
            };
            break;
          }
          case 'twitter_repost': {
            const taskUrl = taskPlatForm === 'TWITTER' ? task?.taskTemplate?.config?.postURL : '';
            const splitUrl = taskUrl?.split('/');
            const postId = splitUrl?.[(splitUrl?.length ?? 0) - 1]?.split('?')?.[0] ?? '';
            const linkByOs = isMobile ? taskUrl : `${TWITTER_TWEET_POST_BASE_URL}?tweet_id=${postId}`;
            result = {
              id: task?.id ?? '',
              campaignId: task?.campaignId ?? '',
              title: 'X(Twitter)でリツイートする',
              required: task?.taskTemplate?.required,
              points: task?.taskTemplate?.points,
              description: (
                <div className="text-[14px]">
                  <p>
                    投稿を再投稿する:
                    <br /> <span className="line-clamp-1 text-ellipsis text-[#04AFAF]">{addIsLinkParam(taskUrl)}</span>
                  </p>
                </div>
              ),
              type: 'OPEN_LINK',
              link: linkByOs,
            };
            break;
          }
          case 'twitter_repost_quote': {
            if (taskPlatForm === 'TWITTER') {
              const taskUrl = task?.taskTemplate?.config?.postURLQuote ?? '';

              const ops = {
                text: `${task?.taskTemplate?.config?.quotePost ?? ''}\n\n${taskUrl ?? ''}`,
              };
              const qs = new URLSearchParams(ops).toString();
              const url = `${TWITTER_COMPOSE_POST_BASE_URL}?${qs}`;

              result = {
                id: task?.id ?? '',
                campaignId: task?.campaignId ?? '',
                title: 'X(Twitter)で引用リツイートする',
                required: task?.taskTemplate?.required,
                points: task?.taskTemplate?.points,
                description: (
                  <div className="text-[14px]">
                    <p className="overflow-hidden text-ellipsis">
                      引用文言: <br />
                      <p
                        dangerouslySetInnerHTML={{
                          __html: convertTextWithURL(task?.taskTemplate?.config?.quotePost) ?? '',
                        }}
                      />
                    </p>
                  </div>
                ),
                type: 'OPEN_LINK',
                link: url,
              };
            }
            break;
          }
          case 'twitter_make_post_with_hashtags': {
            if (taskPlatForm === 'TWITTER') {
              const ops = {
                text: task?.taskTemplate?.config?.defaultPostText ?? '',
              };
              const qs = new URLSearchParams(ops).toString();
              const url = `${TWITTER_COMPOSE_POST_BASE_URL}?${qs}`;
              result = {
                id: task?.id ?? '',
                campaignId: task?.campaignId ?? '',
                title: 'X(Twitter)で指定ハッシュタグ付きの投稿をする',
                required: task?.taskTemplate?.required,
                points: task?.taskTemplate?.points,
                description: (
                  <div className="text-[14px]">
                    <p className="font-bold">{task?.taskTemplate?.config?.taskDescription ?? ''}</p>
                    {task?.taskTemplate?.config?.defaultPostText ? (
                      <p className="overflow-hidden text-ellipsis">
                        指定ハッシュタグ:
                        <br /> {task?.taskTemplate?.config?.defaultPostText}
                      </p>
                    ) : (
                      ''
                    )}
                  </div>
                ),
                type: 'OPEN_LINK',
                link: url,
              };
            }
            break;
          }
          case 'twitter_make_post': {
            if (taskPlatForm === 'TWITTER') {
              const ops = {
                text: task?.taskTemplate?.config?.designatedClassicalChinese ?? '',
              };
              const qs = new URLSearchParams(ops).toString();
              const url = `${TWITTER_COMPOSE_POST_BASE_URL}?${qs}`;
              result = {
                id: task?.id ?? '',
                campaignId: task?.campaignId ?? '',
                title: 'X(Twitter)で指定文言を投稿する',
                required: task?.taskTemplate?.required,
                points: task?.taskTemplate?.points,
                description: (
                  <div className="text-[14px]">
                    {taskPlatForm === 'TWITTER' && task?.taskTemplate?.config?.designatedClassicalChinese ? (
                      <p>
                        指定文言:
                        <br /> {task?.taskTemplate?.config?.designatedClassicalChinese}
                      </p>
                    ) : (
                      ''
                    )}
                  </div>
                ),
                type: 'OPEN_LINK',
                link: url,
              };
            }
            break;
          }
          default:
            return null;
        }
        break;
      case 'VISIT_PAGE': {
        const respUrl = taskPlatForm === 'VISIT_PAGE' ? task?.taskTemplate?.config?.url : '';
        const webUrl = respUrl?.indexOf('http') !== 0 ? `https://${respUrl}` : respUrl;
        result = {
          id: task?.id ?? '',
          campaignId: task?.campaignId ?? '',
          required: task?.taskTemplate?.required,
          points: task?.taskTemplate?.points,
          title: 'Webサイトを訪問する',
          description: (
            <div className="text-[14px]">
              <p className="line-clamp-1 text-ellipsis text-[#04AFAF]">{addIsLinkParam(webUrl)}</p>
            </div>
          ),
          type: 'OPEN_LINK',
          link: webUrl,
        };
        break;
      }
      case 'LINE': {
        result = {
          id: task?.id ?? '',
          campaignId: task?.campaignId ?? '',
          title: 'LINEで友だち登録をする',
          required: task?.taskTemplate?.required,
          points: task?.taskTemplate?.points,
          description: (
            <div className="text-[14px]">
              {taskPlatForm === 'LINE' && task?.taskTemplate?.config?.url ? (
                <p className="line-clamp-1 text-ellipsis text-[#04AFAF]">
                  {addIsLinkParam(task?.taskTemplate?.config?.url)}
                </p>
              ) : (
                ''
              )}
            </div>
          ),
          type: 'OPEN_LINK',
          link: taskPlatForm === 'LINE' ? task?.taskTemplate?.config?.url : '',
        };
        break;
      }
      case 'TIKTOK':
        switch (task?.taskTemplate?.config?.type) {
          case 'letThemWatch': {
            result = {
              id: task?.id ?? '',
              campaignId: task?.campaignId ?? '',
              required: task?.taskTemplate?.required,
              points: task?.taskTemplate?.points,
              title: 'TikTokで視聴する',
              description: (
                <div className="text-[14px]">
                  <p className="line-clamp-1 text-ellipsis text-[#04AFAF]">
                    {taskPlatForm === 'TIKTOK' ? addIsLinkParam(task?.taskTemplate?.config?.linkWatch) : ''}
                  </p>
                </div>
              ),
              type: 'OPEN_LINK',
              link: taskPlatForm === 'TIKTOK' ? task?.taskTemplate?.config?.linkWatch : '',
            };
            break;
          }
          case 'letYouLike': {
            result = {
              id: task?.id ?? '',
              campaignId: task?.campaignId ?? '',
              required: task?.taskTemplate?.required,
              points: task?.taskTemplate?.points,
              title: 'TikTokでいいねする',
              description: (
                <div className="text-[14px]">
                  <p className="line-clamp-1 text-ellipsis text-[#04AFAF]">
                    {taskPlatForm === 'TIKTOK' ? addIsLinkParam(task?.taskTemplate?.config?.linkToLike) : ''}
                  </p>
                </div>
              ),
              type: 'OPEN_LINK',
              link: taskPlatForm === 'TIKTOK' ? task?.taskTemplate?.config?.linkToLike : '',
            };
            break;
          }
          case 'letYouComment': {
            result = {
              id: task?.id ?? '',
              campaignId: task?.campaignId ?? '',
              required: task?.taskTemplate?.required,
              points: task?.taskTemplate?.points,
              title: 'TikTokでコメントする',
              description: (
                <div className="text-[14px]">
                  <p className="line-clamp-1 text-ellipsis text-[#04AFAF]">
                    {taskPlatForm === 'TIKTOK' ? addIsLinkParam(task?.taskTemplate?.config?.linkToComment) : ''}
                  </p>
                </div>
              ),
              type: 'OPEN_LINK',
              link: taskPlatForm === 'TIKTOK' ? task?.taskTemplate?.config?.linkToComment : '',
            };
            break;
          }
          case 'letYouShare': {
            result = {
              id: task?.id ?? '',
              campaignId: task?.campaignId ?? '',
              required: task?.taskTemplate?.required,
              points: task?.taskTemplate?.points,
              title: 'Tiktokで再投稿する',
              description: (
                <div className="text-[14px]">
                  <p className="line-clamp-1 text-ellipsis text-[#04AFAF]">
                    {taskPlatForm === 'TIKTOK' ? addIsLinkParam(task?.taskTemplate?.config?.linkToShare) : ''}
                  </p>
                </div>
              ),
              type: 'OPEN_LINK',
              link: taskPlatForm === 'TIKTOK' ? task?.taskTemplate?.config?.linkToShare : '',
            };
            break;
          }
          case 'makeYouFollow': {
            const userFollow = taskPlatForm === 'TIKTOK' ? task?.taskTemplate?.config?.userFollow : '';
            result = {
              id: task?.id ?? '',
              campaignId: task?.campaignId ?? '',
              title: 'TikTokでフォローする',
              required: task?.taskTemplate?.required,
              points: task?.taskTemplate?.points,

              description: (
                <div className="text-[14px]">
                  <p>
                    <span className="font-bold">{userFollow}</span>のアカウントをフォローしてください
                  </p>
                </div>
              ),
              type: 'OPEN_LINK',
              link: `${TIKTOK_BASE_URL}/${userFollow?.charAt(0) === '@' ? userFollow : `@${userFollow}`}`,
            };
            break;
          }
          default:
            return null;
        }
        break;
      case 'INSTAGRAM':
        switch (task?.taskTemplate?.config?.type) {
          case 'letYouLike': {
            result = {
              id: task?.id ?? '',
              campaignId: task?.campaignId ?? '',
              required: task?.taskTemplate?.required,
              points: task?.taskTemplate?.points,
              title: 'Instagramでいいねする',
              description: (
                <div className="text-[14px]">
                  <p className="line-clamp-1 text-ellipsis text-[#04AFAF]">
                    {taskPlatForm === 'INSTAGRAM' ? addIsLinkParam(task?.taskTemplate?.config?.linkToLike) : ''}
                  </p>
                </div>
              ),
              type: 'OPEN_LINK',
              link: taskPlatForm === 'INSTAGRAM' ? task?.taskTemplate?.config?.linkToLike : '',
            };
            break;
          }
          case 'letYouComment': {
            result = {
              id: task?.id ?? '',
              campaignId: task?.campaignId ?? '',
              required: task?.taskTemplate?.required,
              points: task?.taskTemplate?.points,
              title: 'Instagramでコメントする',
              description: (
                <div className="text-[14px]">
                  <p className="line-clamp-1 text-ellipsis text-[#04AFAF]">
                    {taskPlatForm === 'INSTAGRAM' ? addIsLinkParam(task?.taskTemplate?.config?.linkToComment) : ''}
                  </p>
                </div>
              ),
              type: 'OPEN_LINK',
              link: taskPlatForm === 'INSTAGRAM' ? task?.taskTemplate?.config?.linkToComment : '',
            };
            break;
          }
          case 'letYouShare': {
            result = {
              id: task?.id ?? '',
              campaignId: task?.campaignId ?? '',
              required: task?.taskTemplate?.required,
              points: task?.taskTemplate?.points,
              title: 'Instagramでシェアする',
              description: (
                <div className="text-[14px]">
                  <p className="line-clamp-1 text-ellipsis text-[#04AFAF]">
                    {taskPlatForm === 'INSTAGRAM' ? addIsLinkParam(task?.taskTemplate?.config?.linkToShare) : ''}
                  </p>
                </div>
              ),
              type: 'OPEN_LINK',
              link: taskPlatForm === 'INSTAGRAM' ? task?.taskTemplate?.config?.linkToShare : '',
            };
            break;
          }
          case 'letYouMention': {
            result = {
              id: task?.id ?? '',
              campaignId: task?.campaignId ?? '',
              required: task?.taskTemplate?.required,
              points: task?.taskTemplate?.points,
              title: 'Instagramストーリーでメンション',
              description: (
                <div className="text-[14px]">
                  <p className="line-clamp-1 text-ellipsis text-[#04AFAF]">
                    {taskPlatForm === 'INSTAGRAM' ? addIsLinkParam(task?.taskTemplate?.config?.linkToMention) : ''}
                  </p>
                </div>
              ),
              type: 'OPEN_LINK',
              link: `${INSTAGRAM_BASE_URL}/${
                taskPlatForm === 'INSTAGRAM' ? task?.taskTemplate?.config?.linkToMention : ''
              }`,
            };
            break;
          }
          case 'makeYouFollow': {
            result = {
              id: task?.id ?? '',
              campaignId: task?.campaignId ?? '',
              required: task?.taskTemplate?.required,
              points: task?.taskTemplate?.points,
              title: 'Instagramでフォローする',
              description: (
                <div className="text-[14px]">
                  <p className="text-ellipsis font-bold">
                    {taskPlatForm === 'INSTAGRAM' ? task?.taskTemplate?.config?.linkToFollow : ''}
                    <span className="font-normal">のアカウントをフォローしてください</span>
                  </p>
                </div>
              ),
              type: 'OPEN_LINK',
              link: `${INSTAGRAM_BASE_URL}/${
                taskPlatForm === 'INSTAGRAM' ? task?.taskTemplate?.config?.linkToFollow : ''
              }`,
            };
            break;
          }
          default:
            return null;
        }
        break;
      case 'TELEGRAM':
        switch (task?.taskTemplate?.config?.type) {
          case 'joinChannel': {
            result = {
              id: task?.id ?? '',
              campaignId: task?.campaignId ?? '',
              required: task?.taskTemplate?.required,
              points: task?.taskTemplate?.points,
              title: 'Telegramでチャンネルに参加する',
              description: (
                <div className="text-[14px]">
                  <p className="line-clamp-1 text-ellipsis text-[#04AFAF]">
                    {taskPlatForm === 'TELEGRAM' ? addIsLinkParam(task?.taskTemplate?.config?.linkChannel) : ''}
                  </p>
                </div>
              ),
              type: 'OPEN_LINK',
              link: taskPlatForm === 'TELEGRAM' ? task?.taskTemplate?.config?.linkChannel : '',
            };
            break;
          }
          case 'viewPost': {
            result = {
              id: task?.id ?? '',
              campaignId: task?.campaignId ?? '',
              required: task?.taskTemplate?.required,
              points: task?.taskTemplate?.points,
              title: 'Telegramで投稿を閲覧する',
              description: (
                <div className="text-[14px]">
                  <p>
                    リンク:{' '}
                    <p className="line-clamp-1 text-ellipsis text-[#04AFAF]">
                      {taskPlatForm === 'TELEGRAM' ? addIsLinkParam(task?.taskTemplate?.config?.linkPost) : ''}
                    </p>
                  </p>
                </div>
              ),
              type: 'OPEN_LINK',
              link: taskPlatForm === 'TELEGRAM' ? task?.taskTemplate?.config?.linkPost : '',
            };
            break;
          }
          default:
            return null;
        }
        break;
      case 'DISCORD': {
        result = {
          id: task?.id ?? '',
          campaignId: task?.campaignId ?? '',
          required: task?.taskTemplate?.required,
          points: task?.taskTemplate?.points,
          title: 'DiscordでサーバーにJoinする',
          description: (
            <div className="text-[14px]">
              {taskPlatForm === 'DISCORD' && task?.taskTemplate?.config?.inviteLink ? (
                <p className="line-clamp-1 text-ellipsis text-[#04AFAF]">
                  {addIsLinkParam(task?.taskTemplate?.config?.inviteLink)}
                </p>
              ) : (
                ''
              )}
            </div>
          ),
          type: 'OPEN_LINK',
          link: taskPlatForm === 'DISCORD' ? task?.taskTemplate?.config?.inviteLink : '',
        };
        break;
      }
      case 'CUSTOM': {
        switch (task?.taskTemplate?.config?.type) {
          case 'freeAnswer': {
            result = {
              id: task?.id ?? '',
              campaignId: task?.campaignId ?? '',
              title: 'アンケートに回答する',
              required: task?.taskTemplate?.required,
              points: task?.taskTemplate?.points,
              description: '',
              type: 'FAQ_FREE_TEXT',
              taskInfo: task?.taskTemplate?.config ?? null,
            };
            break;
          }
          case 'formatMultiple': {
            result = {
              id: task?.id ?? '',
              campaignId: task?.campaignId ?? '',
              title: 'アンケートに回答する',
              required: task?.taskTemplate?.required,
              points: task?.taskTemplate?.points,
              description: '',
              type: 'FAQ_CHOOSE_MULTIPLE',
              taskInfo: task?.taskTemplate?.config ?? null,
            };
            break;
          }
          case 'formatSingle': {
            result = {
              id: task?.id ?? '',
              campaignId: task?.campaignId ?? '',
              title: 'アンケートに回答する',
              required: task?.taskTemplate?.required,
              points: task?.taskTemplate?.points,
              description: '',
              type: 'FAQ_CHOOSE_ONE',
              taskInfo: task?.taskTemplate?.config ?? null,
            };
            break;
          }
          default:
            return null;
        }
        break;
      }
      case 'SHARE_URL': {
        result = {
          id: task?.id ?? '',
          campaignId: task?.campaignId ?? '',
          title: 'URLで友達を招待する',
          required: false,
          points: task?.taskTemplate?.points,
          description: '',
          type: 'SHARE_URL',
          taskInfo: task?.taskTemplate?.config ?? null,
        };
        break;
      }
      default:
        return null;
    }
  } catch (err: any) {
    toastMessage(getErrorMessage(err), 'error');
  }

  return result;
};
