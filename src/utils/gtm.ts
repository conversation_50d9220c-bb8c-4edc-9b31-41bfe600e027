// lib/gtm.ts
export const GTM_ID = 'GTM-5ZD527ZH';
interface EventListener {
  type: unknown;
  opt?: unknown;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  fn: any;
  shouldRemoveBeforeNavigation?: boolean;
}
declare global {
  interface Window {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    originalAddEventListener: any;
    allHandlers: EventListener[];
    removeEventListeners: (type: string) => void;
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    android: any;
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    dataLayer: any;
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    webkit: any;
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    AppBridge: any;
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    loginFunction: any; // :point_left:️ turn off type checking
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    showPopupFindAddress: any;
  }
}
interface EventProps {
  action: string;
  category: string;
  label: string;
  value?: string;
}

export const pageview = (url: string) => {
  window.dataLayer.push({
    event: 'pageview',
    page: url,
  });
};

export const event = ({ action, category, label, value = '' }: EventProps) => {
  window.dataLayer.push({
    event: 'customEvent',
    category,
    action,
    label,
    value,
  });
};
