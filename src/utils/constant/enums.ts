export const ErrorCode: { [key: string]: string } = {
  '400': '要求の形式が正しくありません',

  '400002': '無効な注文形式',
  '400003': '無効なクエリ形式',
  '400004': '$Keyは数字でなければならない',
  '400005': '無効なカーソル形式',
  '400006': 'コードの有効期限が切れているか、間違っています',
  '400007': 'まだキャンペーンを完了していません',
  '400008': 'すでにこちらのキャンペーンは応募済みです',
  '400009': 'このキャンペーンはすぐに特典が付与されません',
  '400010': '賞品なし',
  '400011': 'キャンペーンは終了しました',
  '400012': 'すでにこちらのキャンペーンは応募済みです、当選した場合マイページをご確認ください',
  '400013': 'キャンペーンの支払いが完了しました',
  '400014': '管理者は1人以上必要となります',
  '400015': 'このキャンペーンはまだ支払われていません',
  '400017': 'このタスクは完了しました。他のタスクを続けてください。',
  '400018': 'このメールアドレスはすでに存在します',

  '401004': 'キャンペーンを削除できません',
  '401005': 'トークンなし',
  '401007': 'メールアドレスまたはパスワードを間違えています！',
  '404001': '入力されたメールアドレスは存在しません。',
  '404002': 'セッションが見つかりません',
  '404003': '会社が見つかりません',
  '404004': 'タスクが見つかりません',
  '404005': 'Amazonギフトコードが見つかりません',
  '404006': 'キャンペーンが見つかりません',
  '401001': '権限不足',
  '401002': '無効な認証情報',
  '401003': '無効なトークン',

  '409001': 'このメールはすでに存在しています',
  '409002': 'このX(Twitter)アカウントはすでに別のアカウントに連携されています',
  '409003': 'このコードはすでに存在しています',
  '409004': 'このユーザーはすでに別の組織に登録されています。',
  '409005': 'この電話番号はすでに存在しています',
  '429001': 'レート制限超過',
};
export const PREVENT_CORS_URL = 'https://cors.bridged.cc';

export const USER_ROLE = {
  QUESTER: 'QUESTER',
  CREATOR: 'CREATOR',
};

export const PAGINATION_PAGE_SIZE = 20;
export const HOME_PAGINATION_PAGE_SIZE = 4;

export const TWITTER_BASE_URL = 'https://x.com';
export const TWITTER_FOLLOW_BASE_URL = 'https://x.com/intent/follow';
export const TWITTER_TWEET_POST_BASE_URL = 'https://x.com/intent/retweet';
export const TWITTER_COMPOSE_POST_BASE_URL = 'https://x.com/compose/post';

export const TIKTOK_BASE_URL = 'https://www.tiktok.com';
export const INSTAGRAM_BASE_URL = 'https://www.instagram.com';

export const REDIRECT_QUERY_KEY = 'callback';

export const FEE_PAYMENT_CAMPAIGN = 10; //   (%)
export const TAX_PAYMENT_CAMPAIGN = 10; //   (%)

interface ICampaignFormName {
  CAMPAIGN_DELETE: 'CAMPAIGN_DELETE';
  CAMPAIGN_PREVIEW: 'CAMPAIGN_PREVIEW';
  CAMPAIGN_SAVE_DRAFT: 'CAMPAIGN_SAVE_DRAFT';
  CAMPAIGN_SETUP_INFO: 'CAMPAIGN_SETUP_INFO';
  CAMPAIGN_SETUP_TASKS: 'CAMPAIGN_SETUP_TASKS';
  CAMPAIGN_SETUP_REWARDS: 'CAMPAIGN_SETUP_REWARDS';
  CAMPAIGN_CONFIRMATION: 'CAMPAIGN_CONFIRMATION';
  COPPY_CAMPAIGN_LINK: 'COPPY_CAMPAIGN_LINK';
}
export type CampaignFormName = keyof ICampaignFormName;

export const CAMPAIGN_CREATION_FORM_NAME: ICampaignFormName = {
  CAMPAIGN_DELETE: 'CAMPAIGN_DELETE',
  CAMPAIGN_PREVIEW: 'CAMPAIGN_PREVIEW',
  CAMPAIGN_SAVE_DRAFT: 'CAMPAIGN_SAVE_DRAFT',
  CAMPAIGN_SETUP_INFO: 'CAMPAIGN_SETUP_INFO',
  CAMPAIGN_SETUP_TASKS: 'CAMPAIGN_SETUP_TASKS',
  CAMPAIGN_SETUP_REWARDS: 'CAMPAIGN_SETUP_REWARDS',
  CAMPAIGN_CONFIRMATION: 'CAMPAIGN_CONFIRMATION',
  COPPY_CAMPAIGN_LINK: 'COPPY_CAMPAIGN_LINK',
};
