import { FEE_PAYMENT_CAMPAIGN, TAX_PAYMENT_CAMPAIGN } from '@/utils/constant/enums';

export const getFeeCampaign = (price: number) => {
  if (price * FEE_PAYMENT_CAMPAIGN < 100) {
    return 0;
  }
  return Number(((price * FEE_PAYMENT_CAMPAIGN) / 100).toFixed(0));
};

export const getTaxCampaign = (fee: number) => {
  if (fee * TAX_PAYMENT_CAMPAIGN < 100) {
    return 0;
  }
  return Number(((fee * TAX_PAYMENT_CAMPAIGN) / 100).toFixed(0));
};
